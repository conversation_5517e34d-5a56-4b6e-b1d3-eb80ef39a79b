<?php
/**
 * Admin page template for Awesome Reports
 *
 * @var int $wp_count
 * @var int $theme_count
 * @var int $plugin_count
 * @var int $server_count
 * @var array $wp_updates
 * @var array $theme_updates
 * @var array $plugin_updates
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}
?>

<!-- First Toolbar: Logo and Branding -->
<div class="ar-toolbar-primary">
    <a class="ar-logo" href="<?php echo admin_url('admin.php?page=awesome_reports'); ?>">
        <span class="ar-logo-icon">
            <svg xmlns="http://www.w3.org/2000/svg" width="512" height="512" viewBox="0 0 512 512" aria-hidden="true"><defs><linearGradient id="arLogoGrad" x1="0" y1="0" x2="0" y2="1"><stop offset="0%" stop-color="#4ade80"/><stop offset="100%" stop-color="#22c55e"/></linearGradient></defs><rect x="0" y="0" width="512" height="512" rx="72" fill="url(#arLogoGrad)"/><g fill="#fff"><rect width="54.89" height="157.89" x="144.01" y="238.11" rx="12.91"/><rect width="54.89" height="280" x="228.56" y="116" rx="17.19"/><rect width="54.89" height="218.94" x="313.1" y="177.06" rx="15.2"/></g></svg>
        </span>
        <h1 class="ar-logo-text"><?php _e('Awesome Reports', 'awesome-reports'); ?></h1>
    </a>
</div>

<!-- Second Toolbar: Time Filters -->
<div class="ar-toolbar-secondary">
    <div class="ar-filters">
        <label for="ar-time-filter" class="screen-reader-text"><?php _e('Time Filter', 'awesome-reports'); ?></label>
        <select id="ar-time-filter" class="ar-time-filter">
            <option value="today"><?php _e('Today', 'awesome-reports'); ?></option>
            <option value="yesterday"><?php _e('Yesterday', 'awesome-reports'); ?></option>
            <option value="last-7-days"><?php _e('Last 7 Days', 'awesome-reports'); ?></option>
            <option value="last-14-days"><?php _e('Last 14 Days', 'awesome-reports'); ?></option>
            <option value="last-30-days" selected><?php _e('Last 30 Days', 'awesome-reports'); ?></option>
            <option value="last-month"><?php _e('Last Month', 'awesome-reports'); ?></option>
            <option value="this-month"><?php _e('This Month', 'awesome-reports'); ?></option>
            <option value="last-90-days"><?php _e('Last 90 Days', 'awesome-reports'); ?></option>
        </select>
    </div>
</div>

<div class="wrap">
    <div></div>
    <!-- Uptime Section -->
    <h2><?php _e('Uptime', 'awesome-reports'); ?></h2>
    <div class="ar-uptime-grid">
        <!-- Column stack: Status + Overview on one column -->
        <div class="ar-uptime-stack">
            <div class="ar-card ar-uptime-card ar-uptime-status">
                <h3><?php _e('Uptime status', 'awesome-reports'); ?></h3>
                <div class="ar-status-row">
                    <span class="ar-status-dot" aria-hidden="true"></span>
                    <span class="ar-status-text"><?php _e('Up', 'awesome-reports'); ?></span>
                </div>
                <div class="ar-subtle"><?php _e('Currently up for 13 days 21 hours 59 minutes', 'awesome-reports'); ?></div>
            </div>
            <div class="ar-card ar-uptime-card ar-uptime-overview">
                <h3><?php _e('Uptime overview', 'awesome-reports'); ?></h3>
                <div class="ar-pills-panel">
                    <div class="ar-pills-head">
                        <span class="ar-pills-title"><?php _e('Last 30 days', 'awesome-reports'); ?></span>
                        <span class="ar-pills-percent" id="ar-uptime-percent">100%</span>
                    </div>
                    <div id="ar-uptime-pills" class="ar-pills" aria-label="Daily uptime status"></div>
                    <div class="ar-pills-foot"><?php _e('2 incidents, 12 minutes 16 seconds down', 'awesome-reports'); ?></div>
                </div>
            </div>
        </div>

        <!-- Card 2: Response time graph (span 2 columns) -->
        <div class="ar-card ar-uptime-card ar-uptime-response">
            <h3><?php _e('Response time', 'awesome-reports'); ?></h3>
            <div class="ar-chart-wrap">
                <canvas id="ar-response-time-chart" aria-label="Response time" role="img"></canvas>
            </div>
        </div>

        <!-- Card 3: Incidents table on same row -->
        <div class="ar-card ar-uptime-card ar-uptime-incidents">
            <h3><?php _e('Incidents', 'awesome-reports'); ?></h3>
            <table class="ar-table">
                <thead>
                    <tr>
                        <th><?php _e('Date', 'awesome-reports'); ?></th>
                        <th><?php _e('Duration', 'awesome-reports'); ?></th>
                        <th><?php _e('Description', 'awesome-reports'); ?></th>
                        <th><?php _e('Status', 'awesome-reports'); ?></th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>Aug 2, 2025</td>
                        <td>5m</td>
                        <td>Brief outage</td>
                        <td><span class="ar-pill-status resolved"><?php _e('Resolved', 'awesome-reports'); ?></span></td>
                    </tr>
                    <tr>
                        <td>Aug 15, 2025</td>
                        <td>3m</td>
                        <td>API timeout</td>
                        <td><span class="ar-pill-status resolved"><?php _e('Resolved', 'awesome-reports'); ?></span></td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>

    <!-- Software Updates Grid -->
    <h2><?php _e('Updates', 'awesome-reports'); ?></h2>
    <div class="ar-grid">
        <!-- WordPress Core Card -->
        <div class="ar-card ar-update-card">
            <h3><?php _e('WordPress updates', 'awesome-reports'); ?></h3>
            <div class="count">
                <?php echo esc_html($wp_count); ?>
            </div>
            <?php if (!empty($wp_updates)) : ?>
                <table class="ar-table">
                    <thead>
                        <tr>
                            <th><?php _e('Date', 'awesome-reports'); ?></th>
                            <th><?php _e('Name', 'awesome-reports'); ?></th>
                            <th><?php _e('Version', 'awesome-reports'); ?></th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach (array_slice($wp_updates, 0, 4) as $update) : ?>
                            <tr>
                                <td><?php echo esc_html(date('M j, Y', strtotime($update->date))); ?></td>
                                <td><?php echo esc_html($update->name); ?></td>
                                <td><?php echo esc_html($update->version_before); ?> → <?php echo esc_html($update->version_after); ?></td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
                <?php if (count($wp_updates) > 4) : ?>
                    <div class="update-more">
                        <?php printf(__('+ %d more updates', 'awesome-reports'), count($wp_updates) - 4); ?>
                    </div>
                <?php endif; ?>
            <?php else : ?>
                <div class="update-none">
                    <?php _e('No updates for this period', 'awesome-reports'); ?>
                </div>
            <?php endif; ?>
        </div>

        <!-- Themes Card -->
        <div class="ar-card ar-update-card">
            <h3><?php _e('Theme updates', 'awesome-reports'); ?></h3>
            <div class="count">
                <?php echo esc_html($theme_count); ?>
            </div>
            <?php if (!empty($theme_updates)) : ?>
                <table class="ar-table">
                    <thead>
                        <tr>
                            <th><?php _e('Date', 'awesome-reports'); ?></th>
                            <th><?php _e('Name', 'awesome-reports'); ?></th>
                            <th><?php _e('Version', 'awesome-reports'); ?></th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach (array_slice($theme_updates, 0, 4) as $update) : ?>
                            <tr>
                                <td><?php echo esc_html(date('M j, Y', strtotime($update->date))); ?></td>
                                <td><?php echo esc_html($update->name); ?></td>
                                <td><?php echo esc_html($update->version_before); ?> → <?php echo esc_html($update->version_after); ?></td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
                <?php if (count($theme_updates) > 4) : ?>
                    <div class="update-more">
                        <?php printf(__('+ %d more updates', 'awesome-reports'), count($theme_updates) - 4); ?>
                    </div>
                <?php endif; ?>
            <?php else : ?>
                <div class="update-none">
                    <?php _e('No updates for this period', 'awesome-reports'); ?>
                </div>
            <?php endif; ?>
        </div>

        <!-- Plugins Card -->
        <div class="ar-card ar-update-card">
            <h3><?php _e('Plugin updates', 'awesome-reports'); ?></h3>
            <div class="count">
                <?php echo esc_html($plugin_count); ?>
            </div>
            <?php if (!empty($plugin_updates)) : ?>
                <table class="ar-table">
                    <thead>
                        <tr>
                            <th><?php _e('Date', 'awesome-reports'); ?></th>
                            <th><?php _e('Name', 'awesome-reports'); ?></th>
                            <th><?php _e('Version', 'awesome-reports'); ?></th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach (array_slice($plugin_updates, 0, 4) as $update) : ?>
                            <tr>
                                <td><?php echo esc_html(date('M j, Y', strtotime($update->date))); ?></td>
                                <td><?php echo esc_html($update->name); ?></td>
                                <td><?php echo esc_html($update->version_before); ?> → <?php echo esc_html($update->version_after); ?></td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
                <?php if (count($plugin_updates) > 4) : ?>
                    <div class="update-more">
                        <?php printf(__('+ %d more updates', 'awesome-reports'), count($plugin_updates) - 4); ?>
                    </div>
                <?php endif; ?>
            <?php else : ?>
                <div class="update-none">
                    <?php _e('No updates for this period', 'awesome-reports'); ?>
                </div>
            <?php endif; ?>
        </div>

        <!-- Server Card -->
        <div class="ar-card ar-update-card">
            <h3><?php _e('Server updates', 'awesome-reports'); ?></h3>
            <div class="count"><?php echo esc_html($server_count); ?></div>
            <table class="ar-table">
                <thead>
                    <tr>
                        <th><?php _e('Date', 'awesome-reports'); ?></th>
                        <th><?php _e('Name', 'awesome-reports'); ?></th>
                        <th><?php _e('Version', 'awesome-reports'); ?></th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>Aug 1, 2025</td>
                        <td>PHP</td>
                        <td>8.1.12 → 8.1.13</td>
                    </tr>
                    <tr>
                        <td>Aug 4, 2025</td>
                        <td>Nginx</td>
                        <td>1.23.3 → 1.23.4</td>
                    </tr>
                    <tr>
                        <td>Aug 6, 2025</td>
                        <td>MySQL</td>
                        <td>8.0.31 → 8.0.32</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>

</div>