/*! For license information please see admin.js.LICENSE.txt */
!function(){"use strict";function t(t){if(null==t)return window;if("[object Window]"!==t.toString()){var e=t.ownerDocument;return e&&e.defaultView||window}return t}function e(e){return e instanceof t(e).Element||e instanceof Element}function i(e){return e instanceof t(e).HTMLElement||e instanceof HTMLElement}function n(e){return"undefined"!=typeof ShadowRoot&&(e instanceof t(e).ShadowRoot||e instanceof ShadowRoot)}var s=Math.max,o=Math.min,r=Math.round;function a(){var t=navigator.userAgentData;return null!=t&&t.brands&&Array.isArray(t.brands)?t.brands.map(function(t){return t.brand+"/"+t.version}).join(" "):navigator.userAgent}function l(){return!/^((?!chrome|android).)*safari/i.test(a())}function h(n,s,o){void 0===s&&(s=!1),void 0===o&&(o=!1);var a=n.getBoundingClientRect(),h=1,c=1;s&&i(n)&&(h=n.offsetWidth>0&&r(a.width)/n.offsetWidth||1,c=n.offsetHeight>0&&r(a.height)/n.offsetHeight||1);var d=(e(n)?t(n):window).visualViewport,u=!l()&&o,f=(a.left+(u&&d?d.offsetLeft:0))/h,p=(a.top+(u&&d?d.offsetTop:0))/c,g=a.width/h,m=a.height/c;return{width:g,height:m,top:p,right:f+g,bottom:p+m,left:f,x:f,y:p}}function c(e){var i=t(e);return{scrollLeft:i.pageXOffset,scrollTop:i.pageYOffset}}function d(t){return t?(t.nodeName||"").toLowerCase():null}function u(t){return((e(t)?t.ownerDocument:t.document)||window.document).documentElement}function f(t){return h(u(t)).left+c(t).scrollLeft}function p(e){return t(e).getComputedStyle(e)}function g(t){var e=p(t),i=e.overflow,n=e.overflowX,s=e.overflowY;return/auto|scroll|overlay|hidden/.test(i+s+n)}function m(e,n,s){void 0===s&&(s=!1);var o,a,l=i(n),p=i(n)&&function(t){var e=t.getBoundingClientRect(),i=r(e.width)/t.offsetWidth||1,n=r(e.height)/t.offsetHeight||1;return 1!==i||1!==n}(n),m=u(n),b=h(e,p,s),x={scrollLeft:0,scrollTop:0},y={x:0,y:0};return(l||!l&&!s)&&(("body"!==d(n)||g(m))&&(x=(o=n)!==t(o)&&i(o)?{scrollLeft:(a=o).scrollLeft,scrollTop:a.scrollTop}:c(o)),i(n)?((y=h(n,!0)).x+=n.clientLeft,y.y+=n.clientTop):m&&(y.x=f(m))),{x:b.left+x.scrollLeft-y.x,y:b.top+x.scrollTop-y.y,width:b.width,height:b.height}}function b(t){var e=h(t),i=t.offsetWidth,n=t.offsetHeight;return Math.abs(e.width-i)<=1&&(i=e.width),Math.abs(e.height-n)<=1&&(n=e.height),{x:t.offsetLeft,y:t.offsetTop,width:i,height:n}}function x(t){return"html"===d(t)?t:t.assignedSlot||t.parentNode||(n(t)?t.host:null)||u(t)}function y(t){return["html","body","#document"].indexOf(d(t))>=0?t.ownerDocument.body:i(t)&&g(t)?t:y(x(t))}function v(e,i){var n;void 0===i&&(i=[]);var s=y(e),o=s===(null==(n=e.ownerDocument)?void 0:n.body),r=t(s),a=o?[r].concat(r.visualViewport||[],g(s)?s:[]):s,l=i.concat(a);return o?l:l.concat(v(x(a)))}function _(t){return["table","td","th"].indexOf(d(t))>=0}function w(t){return i(t)&&"fixed"!==p(t).position?t.offsetParent:null}function M(e){for(var s=t(e),o=w(e);o&&_(o)&&"static"===p(o).position;)o=w(o);return o&&("html"===d(o)||"body"===d(o)&&"static"===p(o).position)?s:o||function(t){var e=/firefox/i.test(a());if(/Trident/i.test(a())&&i(t)&&"fixed"===p(t).position)return null;var s=x(t);for(n(s)&&(s=s.host);i(s)&&["html","body"].indexOf(d(s))<0;){var o=p(s);if("none"!==o.transform||"none"!==o.perspective||"paint"===o.contain||-1!==["transform","perspective"].indexOf(o.willChange)||e&&"filter"===o.willChange||e&&o.filter&&"none"!==o.filter)return s;s=s.parentNode}return null}(e)||s}var k="top",S="bottom",O="right",D="left",P="auto",A=[k,S,O,D],C="start",T="end",E="viewport",L="popper",R=A.reduce(function(t,e){return t.concat([e+"-"+C,e+"-"+T])},[]),I=[].concat(A,[P]).reduce(function(t,e){return t.concat([e,e+"-"+C,e+"-"+T])},[]),F=["beforeRead","read","afterRead","beforeMain","main","afterMain","beforeWrite","write","afterWrite"];function z(t){var e=new Map,i=new Set,n=[];function s(t){i.add(t.name),[].concat(t.requires||[],t.requiresIfExists||[]).forEach(function(t){if(!i.has(t)){var n=e.get(t);n&&s(n)}}),n.push(t)}return t.forEach(function(t){e.set(t.name,t)}),t.forEach(function(t){i.has(t.name)||s(t)}),n}var V={placement:"bottom",modifiers:[],strategy:"absolute"};function B(){for(var t=arguments.length,e=new Array(t),i=0;i<t;i++)e[i]=arguments[i];return!e.some(function(t){return!(t&&"function"==typeof t.getBoundingClientRect)})}function W(t){void 0===t&&(t={});var i=t,n=i.defaultModifiers,s=void 0===n?[]:n,o=i.defaultOptions,r=void 0===o?V:o;return function(t,i,n){void 0===n&&(n=r);var o,a,l={placement:"bottom",orderedModifiers:[],options:Object.assign({},V,r),modifiersData:{},elements:{reference:t,popper:i},attributes:{},styles:{}},h=[],c=!1,d={state:l,setOptions:function(n){var o="function"==typeof n?n(l.options):n;u(),l.options=Object.assign({},r,l.options,o),l.scrollParents={reference:e(t)?v(t):t.contextElement?v(t.contextElement):[],popper:v(i)};var a,c,f=function(t){var e=z(t);return F.reduce(function(t,i){return t.concat(e.filter(function(t){return t.phase===i}))},[])}((a=[].concat(s,l.options.modifiers),c=a.reduce(function(t,e){var i=t[e.name];return t[e.name]=i?Object.assign({},i,e,{options:Object.assign({},i.options,e.options),data:Object.assign({},i.data,e.data)}):e,t},{}),Object.keys(c).map(function(t){return c[t]})));return l.orderedModifiers=f.filter(function(t){return t.enabled}),l.orderedModifiers.forEach(function(t){var e=t.name,i=t.options,n=void 0===i?{}:i,s=t.effect;if("function"==typeof s){var o=s({state:l,name:e,instance:d,options:n});h.push(o||function(){})}}),d.update()},forceUpdate:function(){if(!c){var t=l.elements,e=t.reference,i=t.popper;if(B(e,i)){l.rects={reference:m(e,M(i),"fixed"===l.options.strategy),popper:b(i)},l.reset=!1,l.placement=l.options.placement,l.orderedModifiers.forEach(function(t){return l.modifiersData[t.name]=Object.assign({},t.data)});for(var n=0;n<l.orderedModifiers.length;n++)if(!0!==l.reset){var s=l.orderedModifiers[n],o=s.fn,r=s.options,a=void 0===r?{}:r,h=s.name;"function"==typeof o&&(l=o({state:l,options:a,name:h,instance:d})||l)}else l.reset=!1,n=-1}}},update:(o=function(){return new Promise(function(t){d.forceUpdate(),t(l)})},function(){return a||(a=new Promise(function(t){Promise.resolve().then(function(){a=void 0,t(o())})})),a}),destroy:function(){u(),c=!0}};if(!B(t,i))return d;function u(){h.forEach(function(t){return t()}),h=[]}return d.setOptions(n).then(function(t){!c&&n.onFirstUpdate&&n.onFirstUpdate(t)}),d}}var j={passive:!0};function N(t){return t.split("-")[0]}function H(t){return t.split("-")[1]}function $(t){return["top","bottom"].indexOf(t)>=0?"x":"y"}function U(t){var e,i=t.reference,n=t.element,s=t.placement,o=s?N(s):null,r=s?H(s):null,a=i.x+i.width/2-n.width/2,l=i.y+i.height/2-n.height/2;switch(o){case k:e={x:a,y:i.y-n.height};break;case S:e={x:a,y:i.y+i.height};break;case O:e={x:i.x+i.width,y:l};break;case D:e={x:i.x-n.width,y:l};break;default:e={x:i.x,y:i.y}}var h=o?$(o):null;if(null!=h){var c="y"===h?"height":"width";switch(r){case C:e[h]=e[h]-(i[c]/2-n[c]/2);break;case T:e[h]=e[h]+(i[c]/2-n[c]/2)}}return e}var Y={top:"auto",right:"auto",bottom:"auto",left:"auto"};function X(e){var i,n=e.popper,s=e.popperRect,o=e.placement,a=e.variation,l=e.offsets,h=e.position,c=e.gpuAcceleration,d=e.adaptive,f=e.roundOffsets,g=e.isFixed,m=l.x,b=void 0===m?0:m,x=l.y,y=void 0===x?0:x,v="function"==typeof f?f({x:b,y:y}):{x:b,y:y};b=v.x,y=v.y;var _=l.hasOwnProperty("x"),w=l.hasOwnProperty("y"),P=D,A=k,C=window;if(d){var E=M(n),L="clientHeight",R="clientWidth";E===t(n)&&"static"!==p(E=u(n)).position&&"absolute"===h&&(L="scrollHeight",R="scrollWidth"),(o===k||(o===D||o===O)&&a===T)&&(A=S,y-=(g&&E===C&&C.visualViewport?C.visualViewport.height:E[L])-s.height,y*=c?1:-1),o!==D&&(o!==k&&o!==S||a!==T)||(P=O,b-=(g&&E===C&&C.visualViewport?C.visualViewport.width:E[R])-s.width,b*=c?1:-1)}var I,F=Object.assign({position:h},d&&Y),z=!0===f?function(t,e){var i=t.x,n=t.y,s=e.devicePixelRatio||1;return{x:r(i*s)/s||0,y:r(n*s)/s||0}}({x:b,y:y},t(n)):{x:b,y:y};return b=z.x,y=z.y,c?Object.assign({},F,((I={})[A]=w?"0":"",I[P]=_?"0":"",I.transform=(C.devicePixelRatio||1)<=1?"translate("+b+"px, "+y+"px)":"translate3d("+b+"px, "+y+"px, 0)",I)):Object.assign({},F,((i={})[A]=w?y+"px":"",i[P]=_?b+"px":"",i.transform="",i))}var q={name:"applyStyles",enabled:!0,phase:"write",fn:function(t){var e=t.state;Object.keys(e.elements).forEach(function(t){var n=e.styles[t]||{},s=e.attributes[t]||{},o=e.elements[t];i(o)&&d(o)&&(Object.assign(o.style,n),Object.keys(s).forEach(function(t){var e=s[t];!1===e?o.removeAttribute(t):o.setAttribute(t,!0===e?"":e)}))})},effect:function(t){var e=t.state,n={popper:{position:e.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};return Object.assign(e.elements.popper.style,n.popper),e.styles=n,e.elements.arrow&&Object.assign(e.elements.arrow.style,n.arrow),function(){Object.keys(e.elements).forEach(function(t){var s=e.elements[t],o=e.attributes[t]||{},r=Object.keys(e.styles.hasOwnProperty(t)?e.styles[t]:n[t]).reduce(function(t,e){return t[e]="",t},{});i(s)&&d(s)&&(Object.assign(s.style,r),Object.keys(o).forEach(function(t){s.removeAttribute(t)}))})}},requires:["computeStyles"]},K={left:"right",right:"left",bottom:"top",top:"bottom"};function G(t){return t.replace(/left|right|bottom|top/g,function(t){return K[t]})}var J={start:"end",end:"start"};function Z(t){return t.replace(/start|end/g,function(t){return J[t]})}function Q(t,e){var i=e.getRootNode&&e.getRootNode();if(t.contains(e))return!0;if(i&&n(i)){var s=e;do{if(s&&t.isSameNode(s))return!0;s=s.parentNode||s.host}while(s)}return!1}function tt(t){return Object.assign({},t,{left:t.x,top:t.y,right:t.x+t.width,bottom:t.y+t.height})}function et(i,n,o){return n===E?tt(function(e,i){var n=t(e),s=u(e),o=n.visualViewport,r=s.clientWidth,a=s.clientHeight,h=0,c=0;if(o){r=o.width,a=o.height;var d=l();(d||!d&&"fixed"===i)&&(h=o.offsetLeft,c=o.offsetTop)}return{width:r,height:a,x:h+f(e),y:c}}(i,o)):e(n)?function(t,e){var i=h(t,!1,"fixed"===e);return i.top=i.top+t.clientTop,i.left=i.left+t.clientLeft,i.bottom=i.top+t.clientHeight,i.right=i.left+t.clientWidth,i.width=t.clientWidth,i.height=t.clientHeight,i.x=i.left,i.y=i.top,i}(n,o):tt(function(t){var e,i=u(t),n=c(t),o=null==(e=t.ownerDocument)?void 0:e.body,r=s(i.scrollWidth,i.clientWidth,o?o.scrollWidth:0,o?o.clientWidth:0),a=s(i.scrollHeight,i.clientHeight,o?o.scrollHeight:0,o?o.clientHeight:0),l=-n.scrollLeft+f(t),h=-n.scrollTop;return"rtl"===p(o||i).direction&&(l+=s(i.clientWidth,o?o.clientWidth:0)-r),{width:r,height:a,x:l,y:h}}(u(i)))}function it(t){return Object.assign({},{top:0,right:0,bottom:0,left:0},t)}function nt(t,e){return e.reduce(function(e,i){return e[i]=t,e},{})}function st(t,n){void 0===n&&(n={});var r=n,a=r.placement,l=void 0===a?t.placement:a,c=r.strategy,f=void 0===c?t.strategy:c,g=r.boundary,m=void 0===g?"clippingParents":g,b=r.rootBoundary,y=void 0===b?E:b,_=r.elementContext,w=void 0===_?L:_,D=r.altBoundary,P=void 0!==D&&D,C=r.padding,T=void 0===C?0:C,R=it("number"!=typeof T?T:nt(T,A)),I=w===L?"reference":L,F=t.rects.popper,z=t.elements[P?I:w],V=function(t,n,r,a){var l="clippingParents"===n?function(t){var n=v(x(t)),s=["absolute","fixed"].indexOf(p(t).position)>=0&&i(t)?M(t):t;return e(s)?n.filter(function(t){return e(t)&&Q(t,s)&&"body"!==d(t)}):[]}(t):[].concat(n),h=[].concat(l,[r]),c=h[0],u=h.reduce(function(e,i){var n=et(t,i,a);return e.top=s(n.top,e.top),e.right=o(n.right,e.right),e.bottom=o(n.bottom,e.bottom),e.left=s(n.left,e.left),e},et(t,c,a));return u.width=u.right-u.left,u.height=u.bottom-u.top,u.x=u.left,u.y=u.top,u}(e(z)?z:z.contextElement||u(t.elements.popper),m,y,f),B=h(t.elements.reference),W=U({reference:B,element:F,strategy:"absolute",placement:l}),j=tt(Object.assign({},F,W)),N=w===L?j:B,H={top:V.top-N.top+R.top,bottom:N.bottom-V.bottom+R.bottom,left:V.left-N.left+R.left,right:N.right-V.right+R.right},$=t.modifiersData.offset;if(w===L&&$){var Y=$[l];Object.keys(H).forEach(function(t){var e=[O,S].indexOf(t)>=0?1:-1,i=[k,S].indexOf(t)>=0?"y":"x";H[t]+=Y[i]*e})}return H}function ot(t,e,i){return s(t,o(e,i))}function rt(t,e,i){return void 0===i&&(i={x:0,y:0}),{top:t.top-e.height-i.y,right:t.right-e.width+i.x,bottom:t.bottom-e.height+i.y,left:t.left-e.width-i.x}}function at(t){return[k,O,S,D].some(function(e){return t[e]>=0})}var lt=W({defaultModifiers:[{name:"eventListeners",enabled:!0,phase:"write",fn:function(){},effect:function(e){var i=e.state,n=e.instance,s=e.options,o=s.scroll,r=void 0===o||o,a=s.resize,l=void 0===a||a,h=t(i.elements.popper),c=[].concat(i.scrollParents.reference,i.scrollParents.popper);return r&&c.forEach(function(t){t.addEventListener("scroll",n.update,j)}),l&&h.addEventListener("resize",n.update,j),function(){r&&c.forEach(function(t){t.removeEventListener("scroll",n.update,j)}),l&&h.removeEventListener("resize",n.update,j)}},data:{}},{name:"popperOffsets",enabled:!0,phase:"read",fn:function(t){var e=t.state,i=t.name;e.modifiersData[i]=U({reference:e.rects.reference,element:e.rects.popper,strategy:"absolute",placement:e.placement})},data:{}},{name:"computeStyles",enabled:!0,phase:"beforeWrite",fn:function(t){var e=t.state,i=t.options,n=i.gpuAcceleration,s=void 0===n||n,o=i.adaptive,r=void 0===o||o,a=i.roundOffsets,l=void 0===a||a,h={placement:N(e.placement),variation:H(e.placement),popper:e.elements.popper,popperRect:e.rects.popper,gpuAcceleration:s,isFixed:"fixed"===e.options.strategy};null!=e.modifiersData.popperOffsets&&(e.styles.popper=Object.assign({},e.styles.popper,X(Object.assign({},h,{offsets:e.modifiersData.popperOffsets,position:e.options.strategy,adaptive:r,roundOffsets:l})))),null!=e.modifiersData.arrow&&(e.styles.arrow=Object.assign({},e.styles.arrow,X(Object.assign({},h,{offsets:e.modifiersData.arrow,position:"absolute",adaptive:!1,roundOffsets:l})))),e.attributes.popper=Object.assign({},e.attributes.popper,{"data-popper-placement":e.placement})},data:{}},q,{name:"offset",enabled:!0,phase:"main",requires:["popperOffsets"],fn:function(t){var e=t.state,i=t.options,n=t.name,s=i.offset,o=void 0===s?[0,0]:s,r=I.reduce(function(t,i){return t[i]=function(t,e,i){var n=N(t),s=[D,k].indexOf(n)>=0?-1:1,o="function"==typeof i?i(Object.assign({},e,{placement:t})):i,r=o[0],a=o[1];return r=r||0,a=(a||0)*s,[D,O].indexOf(n)>=0?{x:a,y:r}:{x:r,y:a}}(i,e.rects,o),t},{}),a=r[e.placement],l=a.x,h=a.y;null!=e.modifiersData.popperOffsets&&(e.modifiersData.popperOffsets.x+=l,e.modifiersData.popperOffsets.y+=h),e.modifiersData[n]=r}},{name:"flip",enabled:!0,phase:"main",fn:function(t){var e=t.state,i=t.options,n=t.name;if(!e.modifiersData[n]._skip){for(var s=i.mainAxis,o=void 0===s||s,r=i.altAxis,a=void 0===r||r,l=i.fallbackPlacements,h=i.padding,c=i.boundary,d=i.rootBoundary,u=i.altBoundary,f=i.flipVariations,p=void 0===f||f,g=i.allowedAutoPlacements,m=e.options.placement,b=N(m),x=l||(b!==m&&p?function(t){if(N(t)===P)return[];var e=G(t);return[Z(t),e,Z(e)]}(m):[G(m)]),y=[m].concat(x).reduce(function(t,i){return t.concat(N(i)===P?function(t,e){void 0===e&&(e={});var i=e,n=i.placement,s=i.boundary,o=i.rootBoundary,r=i.padding,a=i.flipVariations,l=i.allowedAutoPlacements,h=void 0===l?I:l,c=H(n),d=c?a?R:R.filter(function(t){return H(t)===c}):A,u=d.filter(function(t){return h.indexOf(t)>=0});0===u.length&&(u=d);var f=u.reduce(function(e,i){return e[i]=st(t,{placement:i,boundary:s,rootBoundary:o,padding:r})[N(i)],e},{});return Object.keys(f).sort(function(t,e){return f[t]-f[e]})}(e,{placement:i,boundary:c,rootBoundary:d,padding:h,flipVariations:p,allowedAutoPlacements:g}):i)},[]),v=e.rects.reference,_=e.rects.popper,w=new Map,M=!0,T=y[0],E=0;E<y.length;E++){var L=y[E],F=N(L),z=H(L)===C,V=[k,S].indexOf(F)>=0,B=V?"width":"height",W=st(e,{placement:L,boundary:c,rootBoundary:d,altBoundary:u,padding:h}),j=V?z?O:D:z?S:k;v[B]>_[B]&&(j=G(j));var $=G(j),U=[];if(o&&U.push(W[F]<=0),a&&U.push(W[j]<=0,W[$]<=0),U.every(function(t){return t})){T=L,M=!1;break}w.set(L,U)}if(M)for(var Y=function(t){var e=y.find(function(e){var i=w.get(e);if(i)return i.slice(0,t).every(function(t){return t})});if(e)return T=e,"break"},X=p?3:1;X>0&&"break"!==Y(X);X--);e.placement!==T&&(e.modifiersData[n]._skip=!0,e.placement=T,e.reset=!0)}},requiresIfExists:["offset"],data:{_skip:!1}},{name:"preventOverflow",enabled:!0,phase:"main",fn:function(t){var e=t.state,i=t.options,n=t.name,r=i.mainAxis,a=void 0===r||r,l=i.altAxis,h=void 0!==l&&l,c=i.boundary,d=i.rootBoundary,u=i.altBoundary,f=i.padding,p=i.tether,g=void 0===p||p,m=i.tetherOffset,x=void 0===m?0:m,y=st(e,{boundary:c,rootBoundary:d,padding:f,altBoundary:u}),v=N(e.placement),_=H(e.placement),w=!_,P=$(v),A="x"===P?"y":"x",T=e.modifiersData.popperOffsets,E=e.rects.reference,L=e.rects.popper,R="function"==typeof x?x(Object.assign({},e.rects,{placement:e.placement})):x,I="number"==typeof R?{mainAxis:R,altAxis:R}:Object.assign({mainAxis:0,altAxis:0},R),F=e.modifiersData.offset?e.modifiersData.offset[e.placement]:null,z={x:0,y:0};if(T){if(a){var V,B="y"===P?k:D,W="y"===P?S:O,j="y"===P?"height":"width",U=T[P],Y=U+y[B],X=U-y[W],q=g?-L[j]/2:0,K=_===C?E[j]:L[j],G=_===C?-L[j]:-E[j],J=e.elements.arrow,Z=g&&J?b(J):{width:0,height:0},Q=e.modifiersData["arrow#persistent"]?e.modifiersData["arrow#persistent"].padding:{top:0,right:0,bottom:0,left:0},tt=Q[B],et=Q[W],it=ot(0,E[j],Z[j]),nt=w?E[j]/2-q-it-tt-I.mainAxis:K-it-tt-I.mainAxis,rt=w?-E[j]/2+q+it+et+I.mainAxis:G+it+et+I.mainAxis,at=e.elements.arrow&&M(e.elements.arrow),lt=at?"y"===P?at.clientTop||0:at.clientLeft||0:0,ht=null!=(V=null==F?void 0:F[P])?V:0,ct=U+rt-ht,dt=ot(g?o(Y,U+nt-ht-lt):Y,U,g?s(X,ct):X);T[P]=dt,z[P]=dt-U}if(h){var ut,ft="x"===P?k:D,pt="x"===P?S:O,gt=T[A],mt="y"===A?"height":"width",bt=gt+y[ft],xt=gt-y[pt],yt=-1!==[k,D].indexOf(v),vt=null!=(ut=null==F?void 0:F[A])?ut:0,_t=yt?bt:gt-E[mt]-L[mt]-vt+I.altAxis,wt=yt?gt+E[mt]+L[mt]-vt-I.altAxis:xt,Mt=g&&yt?function(t,e,i){var n=ot(t,e,i);return n>i?i:n}(_t,gt,wt):ot(g?_t:bt,gt,g?wt:xt);T[A]=Mt,z[A]=Mt-gt}e.modifiersData[n]=z}},requiresIfExists:["offset"]},{name:"arrow",enabled:!0,phase:"main",fn:function(t){var e,i=t.state,n=t.name,s=t.options,o=i.elements.arrow,r=i.modifiersData.popperOffsets,a=N(i.placement),l=$(a),h=[D,O].indexOf(a)>=0?"height":"width";if(o&&r){var c=function(t,e){return it("number"!=typeof(t="function"==typeof t?t(Object.assign({},e.rects,{placement:e.placement})):t)?t:nt(t,A))}(s.padding,i),d=b(o),u="y"===l?k:D,f="y"===l?S:O,p=i.rects.reference[h]+i.rects.reference[l]-r[l]-i.rects.popper[h],g=r[l]-i.rects.reference[l],m=M(o),x=m?"y"===l?m.clientHeight||0:m.clientWidth||0:0,y=p/2-g/2,v=c[u],_=x-d[h]-c[f],w=x/2-d[h]/2+y,P=ot(v,w,_),C=l;i.modifiersData[n]=((e={})[C]=P,e.centerOffset=P-w,e)}},effect:function(t){var e=t.state,i=t.options.element,n=void 0===i?"[data-popper-arrow]":i;null!=n&&("string"!=typeof n||(n=e.elements.popper.querySelector(n)))&&Q(e.elements.popper,n)&&(e.elements.arrow=n)},requires:["popperOffsets"],requiresIfExists:["preventOverflow"]},{name:"hide",enabled:!0,phase:"main",requiresIfExists:["preventOverflow"],fn:function(t){var e=t.state,i=t.name,n=e.rects.reference,s=e.rects.popper,o=e.modifiersData.preventOverflow,r=st(e,{elementContext:"reference"}),a=st(e,{altBoundary:!0}),l=rt(r,n),h=rt(a,s,o),c=at(l),d=at(h);e.modifiersData[i]={referenceClippingOffsets:l,popperEscapeOffsets:h,isReferenceHidden:c,hasPopperEscaped:d},e.attributes.popper=Object.assign({},e.attributes.popper,{"data-popper-reference-hidden":c,"data-popper-escaped":d})}}]}),ht="tippy-content",ct="tippy-arrow",dt="tippy-svg-arrow",ut={passive:!0,capture:!0},ft=function(){return document.body};function pt(t,e,i){if(Array.isArray(t)){var n=t[e];return null==n?Array.isArray(i)?i[e]:i:n}return t}function gt(t,e){var i={}.toString.call(t);return 0===i.indexOf("[object")&&i.indexOf(e+"]")>-1}function mt(t,e){return"function"==typeof t?t.apply(void 0,e):t}function bt(t,e){return 0===e?t:function(n){clearTimeout(i),i=setTimeout(function(){t(n)},e)};var i}function xt(t){return[].concat(t)}function yt(t,e){-1===t.indexOf(e)&&t.push(e)}function vt(t){return[].slice.call(t)}function _t(t){return Object.keys(t).reduce(function(e,i){return void 0!==t[i]&&(e[i]=t[i]),e},{})}function wt(){return document.createElement("div")}function Mt(t){return["Element","Fragment"].some(function(e){return gt(t,e)})}function kt(t,e){t.forEach(function(t){t&&(t.style.transitionDuration=e+"ms")})}function St(t,e){t.forEach(function(t){t&&t.setAttribute("data-state",e)})}function Ot(t,e,i){var n=e+"EventListener";["transitionend","webkitTransitionEnd"].forEach(function(e){t[n](e,i)})}function Dt(t,e){for(var i=e;i;){var n;if(t.contains(i))return!0;i=null==i.getRootNode||null==(n=i.getRootNode())?void 0:n.host}return!1}var Pt={isTouch:!1},At=0;function Ct(){Pt.isTouch||(Pt.isTouch=!0,window.performance&&document.addEventListener("mousemove",Tt))}function Tt(){var t=performance.now();t-At<20&&(Pt.isTouch=!1,document.removeEventListener("mousemove",Tt)),At=t}function Et(){var t,e=document.activeElement;if((t=e)&&t._tippy&&t._tippy.reference===t){var i=e._tippy;e.blur&&!i.state.isVisible&&e.blur()}}var Lt=!("undefined"==typeof window||"undefined"==typeof document||!window.msCrypto),Rt=Object.assign({appendTo:ft,aria:{content:"auto",expanded:"auto"},delay:0,duration:[300,250],getReferenceClientRect:null,hideOnClick:!0,ignoreAttributes:!1,interactive:!1,interactiveBorder:2,interactiveDebounce:0,moveTransition:"",offset:[0,10],onAfterUpdate:function(){},onBeforeUpdate:function(){},onCreate:function(){},onDestroy:function(){},onHidden:function(){},onHide:function(){},onMount:function(){},onShow:function(){},onShown:function(){},onTrigger:function(){},onUntrigger:function(){},onClickOutside:function(){},placement:"top",plugins:[],popperOptions:{},render:null,showOnCreate:!1,touch:!0,trigger:"mouseenter focus",triggerTarget:null},{animateFill:!1,followCursor:!1,inlinePositioning:!1,sticky:!1},{allowHTML:!1,animation:"fade",arrow:!0,content:"",inertia:!1,maxWidth:350,role:"tooltip",theme:"",zIndex:9999}),It=Object.keys(Rt);function Ft(t){var e=(t.plugins||[]).reduce(function(e,i){var n,s=i.name,o=i.defaultValue;return s&&(e[s]=void 0!==t[s]?t[s]:null!=(n=Rt[s])?n:o),e},{});return Object.assign({},t,e)}function zt(t,e){var i=Object.assign({},e,{content:mt(e.content,[t])},e.ignoreAttributes?{}:function(t,e){return(e?Object.keys(Ft(Object.assign({},Rt,{plugins:e}))):It).reduce(function(e,i){var n=(t.getAttribute("data-tippy-"+i)||"").trim();if(!n)return e;if("content"===i)e[i]=n;else try{e[i]=JSON.parse(n)}catch(t){e[i]=n}return e},{})}(t,e.plugins));return i.aria=Object.assign({},Rt.aria,i.aria),i.aria={expanded:"auto"===i.aria.expanded?e.interactive:i.aria.expanded,content:"auto"===i.aria.content?e.interactive?null:"describedby":i.aria.content},i}function Vt(t,e){t.innerHTML=e}function Bt(t){var e=wt();return!0===t?e.className=ct:(e.className=dt,Mt(t)?e.appendChild(t):Vt(e,t)),e}function Wt(t,e){Mt(e.content)?(Vt(t,""),t.appendChild(e.content)):"function"!=typeof e.content&&(e.allowHTML?Vt(t,e.content):t.textContent=e.content)}function jt(t){var e=t.firstElementChild,i=vt(e.children);return{box:e,content:i.find(function(t){return t.classList.contains(ht)}),arrow:i.find(function(t){return t.classList.contains(ct)||t.classList.contains(dt)}),backdrop:i.find(function(t){return t.classList.contains("tippy-backdrop")})}}function Nt(t){var e=wt(),i=wt();i.className="tippy-box",i.setAttribute("data-state","hidden"),i.setAttribute("tabindex","-1");var n=wt();function s(i,n){var s=jt(e),o=s.box,r=s.content,a=s.arrow;n.theme?o.setAttribute("data-theme",n.theme):o.removeAttribute("data-theme"),"string"==typeof n.animation?o.setAttribute("data-animation",n.animation):o.removeAttribute("data-animation"),n.inertia?o.setAttribute("data-inertia",""):o.removeAttribute("data-inertia"),o.style.maxWidth="number"==typeof n.maxWidth?n.maxWidth+"px":n.maxWidth,n.role?o.setAttribute("role",n.role):o.removeAttribute("role"),i.content===n.content&&i.allowHTML===n.allowHTML||Wt(r,t.props),n.arrow?a?i.arrow!==n.arrow&&(o.removeChild(a),o.appendChild(Bt(n.arrow))):o.appendChild(Bt(n.arrow)):a&&o.removeChild(a)}return n.className=ht,n.setAttribute("data-state","hidden"),Wt(n,t.props),e.appendChild(i),i.appendChild(n),s(t.props,t.props),{popper:e,onUpdate:s}}Nt.$$tippy=!0;var Ht=1,$t=[],Ut=[];function Yt(t,e){var i,n,s,o,r,a,l,h,c=zt(t,Object.assign({},Rt,Ft(_t(e)))),d=!1,u=!1,f=!1,p=!1,g=[],m=bt(X,c.interactiveDebounce),b=Ht++,x=(h=c.plugins).filter(function(t,e){return h.indexOf(t)===e}),y={id:b,reference:t,popper:wt(),popperInstance:null,props:c,state:{isEnabled:!0,isVisible:!1,isDestroyed:!1,isMounted:!1,isShown:!1},plugins:x,clearDelayTimeouts:function(){clearTimeout(i),clearTimeout(n),cancelAnimationFrame(s)},setProps:function(e){if(!y.state.isDestroyed){L("onBeforeUpdate",[y,e]),U();var i=y.props,n=zt(t,Object.assign({},i,_t(e),{ignoreAttributes:!0}));y.props=n,$(),i.interactiveDebounce!==n.interactiveDebounce&&(F(),m=bt(X,n.interactiveDebounce)),i.triggerTarget&&!n.triggerTarget?xt(i.triggerTarget).forEach(function(t){t.removeAttribute("aria-expanded")}):n.triggerTarget&&t.removeAttribute("aria-expanded"),I(),E(),w&&w(i,n),y.popperInstance&&(J(),Q().forEach(function(t){requestAnimationFrame(t._tippy.popperInstance.forceUpdate)})),L("onAfterUpdate",[y,e])}},setContent:function(t){y.setProps({content:t})},show:function(){var t=y.state.isVisible,e=y.state.isDestroyed,i=!y.state.isEnabled,n=Pt.isTouch&&!y.props.touch,s=pt(y.props.duration,0,Rt.duration);if(!(t||e||i||n||P().hasAttribute("disabled")||(L("onShow",[y],!1),!1===y.props.onShow(y)))){if(y.state.isVisible=!0,D()&&(_.style.visibility="visible"),E(),W(),y.state.isMounted||(_.style.transition="none"),D()){var o=C();kt([o.box,o.content],0)}var r,l,h;a=function(){var t;if(y.state.isVisible&&!p){if(p=!0,_.offsetHeight,_.style.transition=y.props.moveTransition,D()&&y.props.animation){var e=C(),i=e.box,n=e.content;kt([i,n],s),St([i,n],"visible")}R(),I(),yt(Ut,y),null==(t=y.popperInstance)||t.forceUpdate(),L("onMount",[y]),y.props.animation&&D()&&function(t){N(t,function(){y.state.isShown=!0,L("onShown",[y])})}(s)}},l=y.props.appendTo,h=P(),(r=y.props.interactive&&l===ft||"parent"===l?h.parentNode:mt(l,[h])).contains(_)||r.appendChild(_),y.state.isMounted=!0,J()}},hide:function(){var t=!y.state.isVisible,e=y.state.isDestroyed,i=!y.state.isEnabled,n=pt(y.props.duration,1,Rt.duration);if(!(t||e||i)&&(L("onHide",[y],!1),!1!==y.props.onHide(y))){if(y.state.isVisible=!1,y.state.isShown=!1,p=!1,d=!1,D()&&(_.style.visibility="hidden"),F(),j(),E(!0),D()){var s=C(),o=s.box,r=s.content;y.props.animation&&(kt([o,r],n),St([o,r],"hidden"))}R(),I(),y.props.animation?D()&&function(t,e){N(t,function(){!y.state.isVisible&&_.parentNode&&_.parentNode.contains(_)&&e()})}(n,y.unmount):y.unmount()}},hideWithInteractivity:function(t){A().addEventListener("mousemove",m),yt($t,m),m(t)},enable:function(){y.state.isEnabled=!0},disable:function(){y.hide(),y.state.isEnabled=!1},unmount:function(){y.state.isVisible&&y.hide(),y.state.isMounted&&(Z(),Q().forEach(function(t){t._tippy.unmount()}),_.parentNode&&_.parentNode.removeChild(_),Ut=Ut.filter(function(t){return t!==y}),y.state.isMounted=!1,L("onHidden",[y]))},destroy:function(){y.state.isDestroyed||(y.clearDelayTimeouts(),y.unmount(),U(),delete t._tippy,y.state.isDestroyed=!0,L("onDestroy",[y]))}};if(!c.render)return y;var v=c.render(y),_=v.popper,w=v.onUpdate;_.setAttribute("data-tippy-root",""),_.id="tippy-"+y.id,y.popper=_,t._tippy=y,_._tippy=y;var M=x.map(function(t){return t.fn(y)}),k=t.hasAttribute("aria-expanded");return $(),I(),E(),L("onCreate",[y]),c.showOnCreate&&tt(),_.addEventListener("mouseenter",function(){y.props.interactive&&y.state.isVisible&&y.clearDelayTimeouts()}),_.addEventListener("mouseleave",function(){y.props.interactive&&y.props.trigger.indexOf("mouseenter")>=0&&A().addEventListener("mousemove",m)}),y;function S(){var t=y.props.touch;return Array.isArray(t)?t:[t,0]}function O(){return"hold"===S()[0]}function D(){var t;return!(null==(t=y.props.render)||!t.$$tippy)}function P(){return l||t}function A(){var t,e,i=P().parentNode;return i?null!=(e=xt(i)[0])&&null!=(t=e.ownerDocument)&&t.body?e.ownerDocument:document:document}function C(){return jt(_)}function T(t){return y.state.isMounted&&!y.state.isVisible||Pt.isTouch||o&&"focus"===o.type?0:pt(y.props.delay,t?0:1,Rt.delay)}function E(t){void 0===t&&(t=!1),_.style.pointerEvents=y.props.interactive&&!t?"":"none",_.style.zIndex=""+y.props.zIndex}function L(t,e,i){var n;void 0===i&&(i=!0),M.forEach(function(i){i[t]&&i[t].apply(i,e)}),i&&(n=y.props)[t].apply(n,e)}function R(){var e=y.props.aria;if(e.content){var i="aria-"+e.content,n=_.id;xt(y.props.triggerTarget||t).forEach(function(t){var e=t.getAttribute(i);if(y.state.isVisible)t.setAttribute(i,e?e+" "+n:n);else{var s=e&&e.replace(n,"").trim();s?t.setAttribute(i,s):t.removeAttribute(i)}})}}function I(){!k&&y.props.aria.expanded&&xt(y.props.triggerTarget||t).forEach(function(t){y.props.interactive?t.setAttribute("aria-expanded",y.state.isVisible&&t===P()?"true":"false"):t.removeAttribute("aria-expanded")})}function F(){A().removeEventListener("mousemove",m),$t=$t.filter(function(t){return t!==m})}function z(e){if(!Pt.isTouch||!f&&"mousedown"!==e.type){var i=e.composedPath&&e.composedPath()[0]||e.target;if(!y.props.interactive||!Dt(_,i)){if(xt(y.props.triggerTarget||t).some(function(t){return Dt(t,i)})){if(Pt.isTouch)return;if(y.state.isVisible&&y.props.trigger.indexOf("click")>=0)return}else L("onClickOutside",[y,e]);!0===y.props.hideOnClick&&(y.clearDelayTimeouts(),y.hide(),u=!0,setTimeout(function(){u=!1}),y.state.isMounted||j())}}}function V(){f=!0}function B(){f=!1}function W(){var t=A();t.addEventListener("mousedown",z,!0),t.addEventListener("touchend",z,ut),t.addEventListener("touchstart",B,ut),t.addEventListener("touchmove",V,ut)}function j(){var t=A();t.removeEventListener("mousedown",z,!0),t.removeEventListener("touchend",z,ut),t.removeEventListener("touchstart",B,ut),t.removeEventListener("touchmove",V,ut)}function N(t,e){var i=C().box;function n(t){t.target===i&&(Ot(i,"remove",n),e())}if(0===t)return e();Ot(i,"remove",r),Ot(i,"add",n),r=n}function H(e,i,n){void 0===n&&(n=!1),xt(y.props.triggerTarget||t).forEach(function(t){t.addEventListener(e,i,n),g.push({node:t,eventType:e,handler:i,options:n})})}function $(){var t;O()&&(H("touchstart",Y,{passive:!0}),H("touchend",q,{passive:!0})),(t=y.props.trigger,t.split(/\s+/).filter(Boolean)).forEach(function(t){if("manual"!==t)switch(H(t,Y),t){case"mouseenter":H("mouseleave",q);break;case"focus":H(Lt?"focusout":"blur",K);break;case"focusin":H("focusout",K)}})}function U(){g.forEach(function(t){var e=t.node,i=t.eventType,n=t.handler,s=t.options;e.removeEventListener(i,n,s)}),g=[]}function Y(t){var e,i=!1;if(y.state.isEnabled&&!G(t)&&!u){var n="focus"===(null==(e=o)?void 0:e.type);o=t,l=t.currentTarget,I(),!y.state.isVisible&&gt(t,"MouseEvent")&&$t.forEach(function(e){return e(t)}),"click"===t.type&&(y.props.trigger.indexOf("mouseenter")<0||d)&&!1!==y.props.hideOnClick&&y.state.isVisible?i=!0:tt(t),"click"===t.type&&(d=!i),i&&!n&&et(t)}}function X(t){var e=t.target,i=P().contains(e)||_.contains(e);if("mousemove"!==t.type||!i){var n=Q().concat(_).map(function(t){var e,i=null==(e=t._tippy.popperInstance)?void 0:e.state;return i?{popperRect:t.getBoundingClientRect(),popperState:i,props:c}:null}).filter(Boolean);(function(t,e){var i=e.clientX,n=e.clientY;return t.every(function(t){var e=t.popperRect,s=t.popperState,o=t.props.interactiveBorder,r=s.placement.split("-")[0],a=s.modifiersData.offset;if(!a)return!0;var l="bottom"===r?a.top.y:0,h="top"===r?a.bottom.y:0,c="right"===r?a.left.x:0,d="left"===r?a.right.x:0,u=e.top-n+l>o,f=n-e.bottom-h>o,p=e.left-i+c>o,g=i-e.right-d>o;return u||f||p||g})})(n,t)&&(F(),et(t))}}function q(t){G(t)||y.props.trigger.indexOf("click")>=0&&d||(y.props.interactive?y.hideWithInteractivity(t):et(t))}function K(t){y.props.trigger.indexOf("focusin")<0&&t.target!==P()||y.props.interactive&&t.relatedTarget&&_.contains(t.relatedTarget)||et(t)}function G(t){return!!Pt.isTouch&&O()!==t.type.indexOf("touch")>=0}function J(){Z();var e=y.props,i=e.popperOptions,n=e.placement,s=e.offset,o=e.getReferenceClientRect,r=e.moveTransition,l=D()?jt(_).arrow:null,h=o?{getBoundingClientRect:o,contextElement:o.contextElement||P()}:t,c=[{name:"offset",options:{offset:s}},{name:"preventOverflow",options:{padding:{top:2,bottom:2,left:5,right:5}}},{name:"flip",options:{padding:5}},{name:"computeStyles",options:{adaptive:!r}},{name:"$$tippy",enabled:!0,phase:"beforeWrite",requires:["computeStyles"],fn:function(t){var e=t.state;if(D()){var i=C().box;["placement","reference-hidden","escaped"].forEach(function(t){"placement"===t?i.setAttribute("data-placement",e.placement):e.attributes.popper["data-popper-"+t]?i.setAttribute("data-"+t,""):i.removeAttribute("data-"+t)}),e.attributes.popper={}}}}];D()&&l&&c.push({name:"arrow",options:{element:l,padding:3}}),c.push.apply(c,(null==i?void 0:i.modifiers)||[]),y.popperInstance=lt(h,_,Object.assign({},i,{placement:n,onFirstUpdate:a,modifiers:c}))}function Z(){y.popperInstance&&(y.popperInstance.destroy(),y.popperInstance=null)}function Q(){return vt(_.querySelectorAll("[data-tippy-root]"))}function tt(t){y.clearDelayTimeouts(),t&&L("onTrigger",[y,t]),W();var e=T(!0),n=S(),s=n[0],o=n[1];Pt.isTouch&&"hold"===s&&o&&(e=o),e?i=setTimeout(function(){y.show()},e):y.show()}function et(t){if(y.clearDelayTimeouts(),L("onUntrigger",[y,t]),y.state.isVisible){if(!(y.props.trigger.indexOf("mouseenter")>=0&&y.props.trigger.indexOf("click")>=0&&["mouseleave","mousemove"].indexOf(t.type)>=0&&d)){var e=T(!1);e?n=setTimeout(function(){y.state.isVisible&&y.hide()},e):s=requestAnimationFrame(function(){y.hide()})}}else j()}}function Xt(t,e){void 0===e&&(e={});var i=Rt.plugins.concat(e.plugins||[]);document.addEventListener("touchstart",Ct,ut),window.addEventListener("blur",Et);var n,s=Object.assign({},e,{plugins:i}),o=(n=t,Mt(n)?[n]:function(t){return gt(t,"NodeList")}(n)?vt(n):Array.isArray(n)?n:vt(document.querySelectorAll(n))).reduce(function(t,e){var i=e&&Yt(e,s);return i&&t.push(i),t},[]);return Mt(t)?o[0]:o}Xt.defaultProps=Rt,Xt.setDefaultProps=function(t){Object.keys(t).forEach(function(e){Rt[e]=t[e]})},Xt.currentInput=Pt,Object.assign({},q,{effect:function(t){var e=t.state,i={popper:{position:e.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};Object.assign(e.elements.popper.style,i.popper),e.styles=i,e.elements.arrow&&Object.assign(e.elements.arrow.style,i.arrow)}}),Xt.setDefaultProps({render:Nt});var qt=Xt;function Kt(t){return t+.5|0}const Gt=(t,e,i)=>Math.max(Math.min(t,i),e);function Jt(t){return Gt(Kt(2.55*t),0,255)}function Zt(t){return Gt(Kt(255*t),0,255)}function Qt(t){return Gt(Kt(t/2.55)/100,0,1)}function te(t){return Gt(Kt(100*t),0,100)}const ee={0:0,1:1,2:2,3:3,4:4,5:5,6:6,7:7,8:8,9:9,A:10,B:11,C:12,D:13,E:14,F:15,a:10,b:11,c:12,d:13,e:14,f:15},ie=[..."0123456789ABCDEF"],ne=t=>ie[15&t],se=t=>ie[(240&t)>>4]+ie[15&t],oe=t=>(240&t)>>4==(15&t);const re=/^(hsla?|hwb|hsv)\(\s*([-+.e\d]+)(?:deg)?[\s,]+([-+.e\d]+)%[\s,]+([-+.e\d]+)%(?:[\s,]+([-+.e\d]+)(%)?)?\s*\)$/;function ae(t,e,i){const n=e*Math.min(i,1-i),s=(e,s=(e+t/30)%12)=>i-n*Math.max(Math.min(s-3,9-s,1),-1);return[s(0),s(8),s(4)]}function le(t,e,i){const n=(n,s=(n+t/60)%6)=>i-i*e*Math.max(Math.min(s,4-s,1),0);return[n(5),n(3),n(1)]}function he(t,e,i){const n=ae(t,1,.5);let s;for(e+i>1&&(s=1/(e+i),e*=s,i*=s),s=0;s<3;s++)n[s]*=1-e-i,n[s]+=e;return n}function ce(t){const e=t.r/255,i=t.g/255,n=t.b/255,s=Math.max(e,i,n),o=Math.min(e,i,n),r=(s+o)/2;let a,l,h;return s!==o&&(h=s-o,l=r>.5?h/(2-s-o):h/(s+o),a=function(t,e,i,n,s){return t===s?(e-i)/n+(e<i?6:0):e===s?(i-t)/n+2:(t-e)/n+4}(e,i,n,h,s),a=60*a+.5),[0|a,l||0,r]}function de(t,e,i,n){return(Array.isArray(e)?t(e[0],e[1],e[2]):t(e,i,n)).map(Zt)}function ue(t,e,i){return de(ae,t,e,i)}function fe(t){return(t%360+360)%360}const pe={x:"dark",Z:"light",Y:"re",X:"blu",W:"gr",V:"medium",U:"slate",A:"ee",T:"ol",S:"or",B:"ra",C:"lateg",D:"ights",R:"in",Q:"turquois",E:"hi",P:"ro",O:"al",N:"le",M:"de",L:"yello",F:"en",K:"ch",G:"arks",H:"ea",I:"ightg",J:"wh"},ge={OiceXe:"f0f8ff",antiquewEte:"faebd7",aqua:"ffff",aquamarRe:"7fffd4",azuY:"f0ffff",beige:"f5f5dc",bisque:"ffe4c4",black:"0",blanKedOmond:"ffebcd",Xe:"ff",XeviTet:"8a2be2",bPwn:"a52a2a",burlywood:"deb887",caMtXe:"5f9ea0",KartYuse:"7fff00",KocTate:"d2691e",cSO:"ff7f50",cSnflowerXe:"6495ed",cSnsilk:"fff8dc",crimson:"dc143c",cyan:"ffff",xXe:"8b",xcyan:"8b8b",xgTMnPd:"b8860b",xWay:"a9a9a9",xgYF:"6400",xgYy:"a9a9a9",xkhaki:"bdb76b",xmagFta:"8b008b",xTivegYF:"556b2f",xSange:"ff8c00",xScEd:"9932cc",xYd:"8b0000",xsOmon:"e9967a",xsHgYF:"8fbc8f",xUXe:"483d8b",xUWay:"2f4f4f",xUgYy:"2f4f4f",xQe:"ced1",xviTet:"9400d3",dAppRk:"ff1493",dApskyXe:"bfff",dimWay:"696969",dimgYy:"696969",dodgerXe:"1e90ff",fiYbrick:"b22222",flSOwEte:"fffaf0",foYstWAn:"228b22",fuKsia:"ff00ff",gaRsbSo:"dcdcdc",ghostwEte:"f8f8ff",gTd:"ffd700",gTMnPd:"daa520",Way:"808080",gYF:"8000",gYFLw:"adff2f",gYy:"808080",honeyMw:"f0fff0",hotpRk:"ff69b4",RdianYd:"cd5c5c",Rdigo:"4b0082",ivSy:"fffff0",khaki:"f0e68c",lavFMr:"e6e6fa",lavFMrXsh:"fff0f5",lawngYF:"7cfc00",NmoncEffon:"fffacd",ZXe:"add8e6",ZcSO:"f08080",Zcyan:"e0ffff",ZgTMnPdLw:"fafad2",ZWay:"d3d3d3",ZgYF:"90ee90",ZgYy:"d3d3d3",ZpRk:"ffb6c1",ZsOmon:"ffa07a",ZsHgYF:"20b2aa",ZskyXe:"87cefa",ZUWay:"778899",ZUgYy:"778899",ZstAlXe:"b0c4de",ZLw:"ffffe0",lime:"ff00",limegYF:"32cd32",lRF:"faf0e6",magFta:"ff00ff",maPon:"800000",VaquamarRe:"66cdaa",VXe:"cd",VScEd:"ba55d3",VpurpN:"9370db",VsHgYF:"3cb371",VUXe:"7b68ee",VsprRggYF:"fa9a",VQe:"48d1cc",VviTetYd:"c71585",midnightXe:"191970",mRtcYam:"f5fffa",mistyPse:"ffe4e1",moccasR:"ffe4b5",navajowEte:"ffdead",navy:"80",Tdlace:"fdf5e6",Tive:"808000",TivedBb:"6b8e23",Sange:"ffa500",SangeYd:"ff4500",ScEd:"da70d6",pOegTMnPd:"eee8aa",pOegYF:"98fb98",pOeQe:"afeeee",pOeviTetYd:"db7093",papayawEp:"ffefd5",pHKpuff:"ffdab9",peru:"cd853f",pRk:"ffc0cb",plum:"dda0dd",powMrXe:"b0e0e6",purpN:"800080",YbeccapurpN:"663399",Yd:"ff0000",Psybrown:"bc8f8f",PyOXe:"4169e1",saddNbPwn:"8b4513",sOmon:"fa8072",sandybPwn:"f4a460",sHgYF:"2e8b57",sHshell:"fff5ee",siFna:"a0522d",silver:"c0c0c0",skyXe:"87ceeb",UXe:"6a5acd",UWay:"708090",UgYy:"708090",snow:"fffafa",sprRggYF:"ff7f",stAlXe:"4682b4",tan:"d2b48c",teO:"8080",tEstN:"d8bfd8",tomato:"ff6347",Qe:"40e0d0",viTet:"ee82ee",JHt:"f5deb3",wEte:"ffffff",wEtesmoke:"f5f5f5",Lw:"ffff00",LwgYF:"9acd32"};let me;const be=/^rgba?\(\s*([-+.\d]+)(%)?[\s,]+([-+.e\d]+)(%)?[\s,]+([-+.e\d]+)(%)?(?:[\s,/]+([-+.e\d]+)(%)?)?\s*\)$/,xe=t=>t<=.0031308?12.92*t:1.055*Math.pow(t,1/2.4)-.055,ye=t=>t<=.04045?t/12.92:Math.pow((t+.055)/1.055,2.4);function ve(t,e,i){if(t){let n=ce(t);n[e]=Math.max(0,Math.min(n[e]+n[e]*i,0===e?360:1)),n=ue(n),t.r=n[0],t.g=n[1],t.b=n[2]}}function _e(t,e){return t?Object.assign(e||{},t):t}function we(t){var e={r:0,g:0,b:0,a:255};return Array.isArray(t)?t.length>=3&&(e={r:t[0],g:t[1],b:t[2],a:255},t.length>3&&(e.a=Zt(t[3]))):(e=_e(t,{r:0,g:0,b:0,a:1})).a=Zt(e.a),e}function Me(t){return"r"===t.charAt(0)?function(t){const e=be.exec(t);let i,n,s,o=255;if(e){if(e[7]!==i){const t=+e[7];o=e[8]?Jt(t):Gt(255*t,0,255)}return i=+e[1],n=+e[3],s=+e[5],i=255&(e[2]?Jt(i):Gt(i,0,255)),n=255&(e[4]?Jt(n):Gt(n,0,255)),s=255&(e[6]?Jt(s):Gt(s,0,255)),{r:i,g:n,b:s,a:o}}}(t):function(t){const e=re.exec(t);let i,n=255;if(!e)return;e[5]!==i&&(n=e[6]?Jt(+e[5]):Zt(+e[5]));const s=fe(+e[2]),o=+e[3]/100,r=+e[4]/100;return i="hwb"===e[1]?function(t,e,i){return de(he,t,e,i)}(s,o,r):"hsv"===e[1]?function(t,e,i){return de(le,t,e,i)}(s,o,r):ue(s,o,r),{r:i[0],g:i[1],b:i[2],a:n}}(t)}class ke{constructor(t){if(t instanceof ke)return t;const e=typeof t;let i;var n,s,o;"object"===e?i=we(t):"string"===e&&(o=(n=t).length,"#"===n[0]&&(4===o||5===o?s={r:255&17*ee[n[1]],g:255&17*ee[n[2]],b:255&17*ee[n[3]],a:5===o?17*ee[n[4]]:255}:7!==o&&9!==o||(s={r:ee[n[1]]<<4|ee[n[2]],g:ee[n[3]]<<4|ee[n[4]],b:ee[n[5]]<<4|ee[n[6]],a:9===o?ee[n[7]]<<4|ee[n[8]]:255})),i=s||function(t){me||(me=function(){const t={},e=Object.keys(ge),i=Object.keys(pe);let n,s,o,r,a;for(n=0;n<e.length;n++){for(r=a=e[n],s=0;s<i.length;s++)o=i[s],a=a.replace(o,pe[o]);o=parseInt(ge[r],16),t[a]=[o>>16&255,o>>8&255,255&o]}return t}(),me.transparent=[0,0,0,0]);const e=me[t.toLowerCase()];return e&&{r:e[0],g:e[1],b:e[2],a:4===e.length?e[3]:255}}(t)||Me(t)),this._rgb=i,this._valid=!!i}get valid(){return this._valid}get rgb(){var t=_e(this._rgb);return t&&(t.a=Qt(t.a)),t}set rgb(t){this._rgb=we(t)}rgbString(){return this._valid?(t=this._rgb)&&(t.a<255?`rgba(${t.r}, ${t.g}, ${t.b}, ${Qt(t.a)})`:`rgb(${t.r}, ${t.g}, ${t.b})`):void 0;var t}hexString(){return this._valid?(t=this._rgb,e=(t=>oe(t.r)&&oe(t.g)&&oe(t.b)&&oe(t.a))(t)?ne:se,t?"#"+e(t.r)+e(t.g)+e(t.b)+((t,e)=>t<255?e(t):"")(t.a,e):void 0):void 0;var t,e}hslString(){return this._valid?function(t){if(!t)return;const e=ce(t),i=e[0],n=te(e[1]),s=te(e[2]);return t.a<255?`hsla(${i}, ${n}%, ${s}%, ${Qt(t.a)})`:`hsl(${i}, ${n}%, ${s}%)`}(this._rgb):void 0}mix(t,e){if(t){const i=this.rgb,n=t.rgb;let s;const o=e===s?.5:e,r=2*o-1,a=i.a-n.a,l=((r*a===-1?r:(r+a)/(1+r*a))+1)/2;s=1-l,i.r=255&l*i.r+s*n.r+.5,i.g=255&l*i.g+s*n.g+.5,i.b=255&l*i.b+s*n.b+.5,i.a=o*i.a+(1-o)*n.a,this.rgb=i}return this}interpolate(t,e){return t&&(this._rgb=function(t,e,i){const n=ye(Qt(t.r)),s=ye(Qt(t.g)),o=ye(Qt(t.b));return{r:Zt(xe(n+i*(ye(Qt(e.r))-n))),g:Zt(xe(s+i*(ye(Qt(e.g))-s))),b:Zt(xe(o+i*(ye(Qt(e.b))-o))),a:t.a+i*(e.a-t.a)}}(this._rgb,t._rgb,e)),this}clone(){return new ke(this.rgb)}alpha(t){return this._rgb.a=Zt(t),this}clearer(t){return this._rgb.a*=1-t,this}greyscale(){const t=this._rgb,e=Kt(.3*t.r+.59*t.g+.11*t.b);return t.r=t.g=t.b=e,this}opaquer(t){return this._rgb.a*=1+t,this}negate(){const t=this._rgb;return t.r=255-t.r,t.g=255-t.g,t.b=255-t.b,this}lighten(t){return ve(this._rgb,2,t),this}darken(t){return ve(this._rgb,2,-t),this}saturate(t){return ve(this._rgb,1,t),this}desaturate(t){return ve(this._rgb,1,-t),this}rotate(t){return function(t,e){var i=ce(t);i[0]=fe(i[0]+e),i=ue(i),t.r=i[0],t.g=i[1],t.b=i[2]}(this._rgb,t),this}}function Se(){}const Oe=(()=>{let t=0;return()=>t++})();function De(t){return null==t}function Pe(t){if(Array.isArray&&Array.isArray(t))return!0;const e=Object.prototype.toString.call(t);return"[object"===e.slice(0,7)&&"Array]"===e.slice(-6)}function Ae(t){return null!==t&&"[object Object]"===Object.prototype.toString.call(t)}function Ce(t){return("number"==typeof t||t instanceof Number)&&isFinite(+t)}function Te(t,e){return Ce(t)?t:e}function Ee(t,e){return void 0===t?e:t}const Le=(t,e)=>"string"==typeof t&&t.endsWith("%")?parseFloat(t)/100*e:+t;function Re(t,e,i){if(t&&"function"==typeof t.call)return t.apply(i,e)}function Ie(t,e,i,n){let s,o,r;if(Pe(t))if(o=t.length,n)for(s=o-1;s>=0;s--)e.call(i,t[s],s);else for(s=0;s<o;s++)e.call(i,t[s],s);else if(Ae(t))for(r=Object.keys(t),o=r.length,s=0;s<o;s++)e.call(i,t[r[s]],r[s])}function Fe(t,e){let i,n,s,o;if(!t||!e||t.length!==e.length)return!1;for(i=0,n=t.length;i<n;++i)if(s=t[i],o=e[i],s.datasetIndex!==o.datasetIndex||s.index!==o.index)return!1;return!0}function ze(t){if(Pe(t))return t.map(ze);if(Ae(t)){const e=Object.create(null),i=Object.keys(t),n=i.length;let s=0;for(;s<n;++s)e[i[s]]=ze(t[i[s]]);return e}return t}function Ve(t){return-1===["__proto__","prototype","constructor"].indexOf(t)}function Be(t,e,i,n){if(!Ve(t))return;const s=e[t],o=i[t];Ae(s)&&Ae(o)?We(s,o,n):e[t]=ze(o)}function We(t,e,i){const n=Pe(e)?e:[e],s=n.length;if(!Ae(t))return t;const o=(i=i||{}).merger||Be;let r;for(let e=0;e<s;++e){if(r=n[e],!Ae(r))continue;const s=Object.keys(r);for(let e=0,n=s.length;e<n;++e)o(s[e],t,r,i)}return t}function je(t,e){return We(t,e,{merger:Ne})}function Ne(t,e,i){if(!Ve(t))return;const n=e[t],s=i[t];Ae(n)&&Ae(s)?je(n,s):Object.prototype.hasOwnProperty.call(e,t)||(e[t]=ze(s))}const He={"":t=>t,x:t=>t.x,y:t=>t.y};function $e(t,e){const i=He[e]||(He[e]=function(t){const e=function(t){const e=t.split("."),i=[];let n="";for(const t of e)n+=t,n.endsWith("\\")?n=n.slice(0,-1)+".":(i.push(n),n="");return i}(t);return t=>{for(const i of e){if(""===i)break;t=t&&t[i]}return t}}(e));return i(t)}function Ue(t){return t.charAt(0).toUpperCase()+t.slice(1)}const Ye=t=>void 0!==t,Xe=t=>"function"==typeof t,qe=(t,e)=>{if(t.size!==e.size)return!1;for(const i of t)if(!e.has(i))return!1;return!0},Ke=Math.PI,Ge=2*Ke,Je=Ge+Ke,Ze=Number.POSITIVE_INFINITY,Qe=Ke/180,ti=Ke/2,ei=Ke/4,ii=2*Ke/3,ni=Math.log10,si=Math.sign;function oi(t,e,i){return Math.abs(t-e)<i}function ri(t){const e=Math.round(t);t=oi(t,e,t/1e3)?e:t;const i=Math.pow(10,Math.floor(ni(t))),n=t/i;return(n<=1?1:n<=2?2:n<=5?5:10)*i}function ai(t){return!function(t){return"symbol"==typeof t||"object"==typeof t&&null!==t&&!(Symbol.toPrimitive in t||"toString"in t||"valueOf"in t)}(t)&&!isNaN(parseFloat(t))&&isFinite(t)}function li(t,e,i){let n,s,o;for(n=0,s=t.length;n<s;n++)o=t[n][i],isNaN(o)||(e.min=Math.min(e.min,o),e.max=Math.max(e.max,o))}function hi(t){return t*(Ke/180)}function ci(t){return t*(180/Ke)}function di(t){if(!Ce(t))return;let e=1,i=0;for(;Math.round(t*e)/e!==t;)e*=10,i++;return i}function ui(t,e){const i=e.x-t.x,n=e.y-t.y,s=Math.sqrt(i*i+n*n);let o=Math.atan2(n,i);return o<-.5*Ke&&(o+=Ge),{angle:o,distance:s}}function fi(t,e){return Math.sqrt(Math.pow(e.x-t.x,2)+Math.pow(e.y-t.y,2))}function pi(t,e){return(t-e+Je)%Ge-Ke}function gi(t){return(t%Ge+Ge)%Ge}function mi(t,e,i,n){const s=gi(t),o=gi(e),r=gi(i),a=gi(o-s),l=gi(r-s),h=gi(s-o),c=gi(s-r);return s===o||s===r||n&&o===r||a>l&&h<c}function bi(t,e,i){return Math.max(e,Math.min(i,t))}function xi(t,e,i,n=1e-6){return t>=Math.min(e,i)-n&&t<=Math.max(e,i)+n}function yi(t,e,i){i=i||(i=>t[i]<e);let n,s=t.length-1,o=0;for(;s-o>1;)n=o+s>>1,i(n)?o=n:s=n;return{lo:o,hi:s}}const vi=(t,e,i,n)=>yi(t,i,n?n=>{const s=t[n][e];return s<i||s===i&&t[n+1][e]===i}:n=>t[n][e]<i),_i=(t,e,i)=>yi(t,i,n=>t[n][e]>=i),wi=["push","pop","shift","splice","unshift"];function Mi(t,e){const i=t._chartjs;if(!i)return;const n=i.listeners,s=n.indexOf(e);-1!==s&&n.splice(s,1),n.length>0||(wi.forEach(e=>{delete t[e]}),delete t._chartjs)}function ki(t){const e=new Set(t);return e.size===t.length?t:Array.from(e)}const Si="undefined"==typeof window?function(t){return t()}:window.requestAnimationFrame;function Oi(t,e){let i=[],n=!1;return function(...s){i=s,n||(n=!0,Si.call(window,()=>{n=!1,t.apply(e,i)}))}}const Di=t=>"start"===t?"left":"end"===t?"right":"center",Pi=(t,e,i)=>"start"===t?e:"end"===t?i:(e+i)/2;function Ai(t,e,i){const n=e.length;let s=0,o=n;if(t._sorted){const{iScale:r,vScale:a,_parsed:l}=t,h=t.dataset&&t.dataset.options?t.dataset.options.spanGaps:null,c=r.axis,{min:d,max:u,minDefined:f,maxDefined:p}=r.getUserBounds();if(f){if(s=Math.min(vi(l,c,d).lo,i?n:vi(e,c,r.getPixelForValue(d)).lo),h){const t=l.slice(0,s+1).reverse().findIndex(t=>!De(t[a.axis]));s-=Math.max(0,t)}s=bi(s,0,n-1)}if(p){let t=Math.max(vi(l,r.axis,u,!0).hi+1,i?0:vi(e,c,r.getPixelForValue(u),!0).hi+1);if(h){const e=l.slice(t-1).findIndex(t=>!De(t[a.axis]));t+=Math.max(0,e)}o=bi(t,s,n)-s}else o=n-s}return{start:s,count:o}}function Ci(t){const{xScale:e,yScale:i,_scaleRanges:n}=t,s={xmin:e.min,xmax:e.max,ymin:i.min,ymax:i.max};if(!n)return t._scaleRanges=s,!0;const o=n.xmin!==e.min||n.xmax!==e.max||n.ymin!==i.min||n.ymax!==i.max;return Object.assign(n,s),o}const Ti=t=>0===t||1===t,Ei=(t,e,i)=>-Math.pow(2,10*(t-=1))*Math.sin((t-e)*Ge/i),Li=(t,e,i)=>Math.pow(2,-10*t)*Math.sin((t-e)*Ge/i)+1,Ri={linear:t=>t,easeInQuad:t=>t*t,easeOutQuad:t=>-t*(t-2),easeInOutQuad:t=>(t/=.5)<1?.5*t*t:-.5*(--t*(t-2)-1),easeInCubic:t=>t*t*t,easeOutCubic:t=>(t-=1)*t*t+1,easeInOutCubic:t=>(t/=.5)<1?.5*t*t*t:.5*((t-=2)*t*t+2),easeInQuart:t=>t*t*t*t,easeOutQuart:t=>-((t-=1)*t*t*t-1),easeInOutQuart:t=>(t/=.5)<1?.5*t*t*t*t:-.5*((t-=2)*t*t*t-2),easeInQuint:t=>t*t*t*t*t,easeOutQuint:t=>(t-=1)*t*t*t*t+1,easeInOutQuint:t=>(t/=.5)<1?.5*t*t*t*t*t:.5*((t-=2)*t*t*t*t+2),easeInSine:t=>1-Math.cos(t*ti),easeOutSine:t=>Math.sin(t*ti),easeInOutSine:t=>-.5*(Math.cos(Ke*t)-1),easeInExpo:t=>0===t?0:Math.pow(2,10*(t-1)),easeOutExpo:t=>1===t?1:1-Math.pow(2,-10*t),easeInOutExpo:t=>Ti(t)?t:t<.5?.5*Math.pow(2,10*(2*t-1)):.5*(2-Math.pow(2,-10*(2*t-1))),easeInCirc:t=>t>=1?t:-(Math.sqrt(1-t*t)-1),easeOutCirc:t=>Math.sqrt(1-(t-=1)*t),easeInOutCirc:t=>(t/=.5)<1?-.5*(Math.sqrt(1-t*t)-1):.5*(Math.sqrt(1-(t-=2)*t)+1),easeInElastic:t=>Ti(t)?t:Ei(t,.075,.3),easeOutElastic:t=>Ti(t)?t:Li(t,.075,.3),easeInOutElastic(t){const e=.1125;return Ti(t)?t:t<.5?.5*Ei(2*t,e,.45):.5+.5*Li(2*t-1,e,.45)},easeInBack(t){const e=1.70158;return t*t*((e+1)*t-e)},easeOutBack(t){const e=1.70158;return(t-=1)*t*((e+1)*t+e)+1},easeInOutBack(t){let e=1.70158;return(t/=.5)<1?t*t*((1+(e*=1.525))*t-e)*.5:.5*((t-=2)*t*((1+(e*=1.525))*t+e)+2)},easeInBounce:t=>1-Ri.easeOutBounce(1-t),easeOutBounce(t){const e=7.5625,i=2.75;return t<1/i?e*t*t:t<2/i?e*(t-=1.5/i)*t+.75:t<2.5/i?e*(t-=2.25/i)*t+.9375:e*(t-=2.625/i)*t+.984375},easeInOutBounce:t=>t<.5?.5*Ri.easeInBounce(2*t):.5*Ri.easeOutBounce(2*t-1)+.5};function Ii(t){if(t&&"object"==typeof t){const e=t.toString();return"[object CanvasPattern]"===e||"[object CanvasGradient]"===e}return!1}function Fi(t){return Ii(t)?t:new ke(t)}function zi(t){return Ii(t)?t:new ke(t).saturate(.5).darken(.1).hexString()}const Vi=["x","y","borderWidth","radius","tension"],Bi=["color","borderColor","backgroundColor"],Wi=new Map;function ji(t,e,i){return function(t,e){e=e||{};const i=t+JSON.stringify(e);let n=Wi.get(i);return n||(n=new Intl.NumberFormat(t,e),Wi.set(i,n)),n}(e,i).format(t)}const Ni={values(t){return Pe(t)?t:""+t},numeric(t,e,i){if(0===t)return"0";const n=this.chart.options.locale;let s,o=t;if(i.length>1){const e=Math.max(Math.abs(i[0].value),Math.abs(i[i.length-1].value));(e<1e-4||e>1e15)&&(s="scientific"),o=function(t,e){let i=e.length>3?e[2].value-e[1].value:e[1].value-e[0].value;return Math.abs(i)>=1&&t!==Math.floor(t)&&(i=t-Math.floor(t)),i}(t,i)}const r=ni(Math.abs(o)),a=isNaN(r)?1:Math.max(Math.min(-1*Math.floor(r),20),0),l={notation:s,minimumFractionDigits:a,maximumFractionDigits:a};return Object.assign(l,this.options.ticks.format),ji(t,n,l)},logarithmic(t,e,i){if(0===t)return"0";const n=i[e].significand||t/Math.pow(10,Math.floor(ni(t)));return[1,2,3,5,10,15].includes(n)||e>.8*i.length?Ni.numeric.call(this,t,e,i):""}};var Hi={formatters:Ni};const $i=Object.create(null),Ui=Object.create(null);function Yi(t,e){if(!e)return t;const i=e.split(".");for(let e=0,n=i.length;e<n;++e){const n=i[e];t=t[n]||(t[n]=Object.create(null))}return t}function Xi(t,e,i){return"string"==typeof e?We(Yi(t,e),i):We(Yi(t,""),e)}class qi{constructor(t,e){this.animation=void 0,this.backgroundColor="rgba(0,0,0,0.1)",this.borderColor="rgba(0,0,0,0.1)",this.color="#666",this.datasets={},this.devicePixelRatio=t=>t.chart.platform.getDevicePixelRatio(),this.elements={},this.events=["mousemove","mouseout","click","touchstart","touchmove"],this.font={family:"'Helvetica Neue', 'Helvetica', 'Arial', sans-serif",size:12,style:"normal",lineHeight:1.2,weight:null},this.hover={},this.hoverBackgroundColor=(t,e)=>zi(e.backgroundColor),this.hoverBorderColor=(t,e)=>zi(e.borderColor),this.hoverColor=(t,e)=>zi(e.color),this.indexAxis="x",this.interaction={mode:"nearest",intersect:!0,includeInvisible:!1},this.maintainAspectRatio=!0,this.onHover=null,this.onClick=null,this.parsing=!0,this.plugins={},this.responsive=!0,this.scale=void 0,this.scales={},this.showLine=!0,this.drawActiveElementsOnTop=!0,this.describe(t),this.apply(e)}set(t,e){return Xi(this,t,e)}get(t){return Yi(this,t)}describe(t,e){return Xi(Ui,t,e)}override(t,e){return Xi($i,t,e)}route(t,e,i,n){const s=Yi(this,t),o=Yi(this,i),r="_"+e;Object.defineProperties(s,{[r]:{value:s[e],writable:!0},[e]:{enumerable:!0,get(){const t=this[r],e=o[n];return Ae(t)?Object.assign({},e,t):Ee(t,e)},set(t){this[r]=t}}})}apply(t){t.forEach(t=>t(this))}}var Ki=new qi({_scriptable:t=>!t.startsWith("on"),_indexable:t=>"events"!==t,hover:{_fallback:"interaction"},interaction:{_scriptable:!1,_indexable:!1}},[function(t){t.set("animation",{delay:void 0,duration:1e3,easing:"easeOutQuart",fn:void 0,from:void 0,loop:void 0,to:void 0,type:void 0}),t.describe("animation",{_fallback:!1,_indexable:!1,_scriptable:t=>"onProgress"!==t&&"onComplete"!==t&&"fn"!==t}),t.set("animations",{colors:{type:"color",properties:Bi},numbers:{type:"number",properties:Vi}}),t.describe("animations",{_fallback:"animation"}),t.set("transitions",{active:{animation:{duration:400}},resize:{animation:{duration:0}},show:{animations:{colors:{from:"transparent"},visible:{type:"boolean",duration:0}}},hide:{animations:{colors:{to:"transparent"},visible:{type:"boolean",easing:"linear",fn:t=>0|t}}}})},function(t){t.set("layout",{autoPadding:!0,padding:{top:0,right:0,bottom:0,left:0}})},function(t){t.set("scale",{display:!0,offset:!1,reverse:!1,beginAtZero:!1,bounds:"ticks",clip:!0,grace:0,grid:{display:!0,lineWidth:1,drawOnChartArea:!0,drawTicks:!0,tickLength:8,tickWidth:(t,e)=>e.lineWidth,tickColor:(t,e)=>e.color,offset:!1},border:{display:!0,dash:[],dashOffset:0,width:1},title:{display:!1,text:"",padding:{top:4,bottom:4}},ticks:{minRotation:0,maxRotation:50,mirror:!1,textStrokeWidth:0,textStrokeColor:"",padding:3,display:!0,autoSkip:!0,autoSkipPadding:3,labelOffset:0,callback:Hi.formatters.values,minor:{},major:{},align:"center",crossAlign:"near",showLabelBackdrop:!1,backdropColor:"rgba(255, 255, 255, 0.75)",backdropPadding:2}}),t.route("scale.ticks","color","","color"),t.route("scale.grid","color","","borderColor"),t.route("scale.border","color","","borderColor"),t.route("scale.title","color","","color"),t.describe("scale",{_fallback:!1,_scriptable:t=>!t.startsWith("before")&&!t.startsWith("after")&&"callback"!==t&&"parser"!==t,_indexable:t=>"borderDash"!==t&&"tickBorderDash"!==t&&"dash"!==t}),t.describe("scales",{_fallback:"scale"}),t.describe("scale.ticks",{_scriptable:t=>"backdropPadding"!==t&&"callback"!==t,_indexable:t=>"backdropPadding"!==t})}]);function Gi(t,e,i,n,s){let o=e[s];return o||(o=e[s]=t.measureText(s).width,i.push(s)),o>n&&(n=o),n}function Ji(t,e,i,n){let s=(n=n||{}).data=n.data||{},o=n.garbageCollect=n.garbageCollect||[];n.font!==e&&(s=n.data={},o=n.garbageCollect=[],n.font=e),t.save(),t.font=e;let r=0;const a=i.length;let l,h,c,d,u;for(l=0;l<a;l++)if(d=i[l],null==d||Pe(d)){if(Pe(d))for(h=0,c=d.length;h<c;h++)u=d[h],null==u||Pe(u)||(r=Gi(t,s,o,r,u))}else r=Gi(t,s,o,r,d);t.restore();const f=o.length/2;if(f>i.length){for(l=0;l<f;l++)delete s[o[l]];o.splice(0,f)}return r}function Zi(t,e,i){const n=t.currentDevicePixelRatio,s=0!==i?Math.max(i/2,.5):0;return Math.round((e-s)*n)/n+s}function Qi(t,e){(e||t)&&((e=e||t.getContext("2d")).save(),e.resetTransform(),e.clearRect(0,0,t.width,t.height),e.restore())}function tn(t,e,i,n){en(t,e,i,n,null)}function en(t,e,i,n,s){let o,r,a,l,h,c,d,u;const f=e.pointStyle,p=e.rotation,g=e.radius;let m=(p||0)*Qe;if(f&&"object"==typeof f&&(o=f.toString(),"[object HTMLImageElement]"===o||"[object HTMLCanvasElement]"===o))return t.save(),t.translate(i,n),t.rotate(m),t.drawImage(f,-f.width/2,-f.height/2,f.width,f.height),void t.restore();if(!(isNaN(g)||g<=0)){switch(t.beginPath(),f){default:s?t.ellipse(i,n,s/2,g,0,0,Ge):t.arc(i,n,g,0,Ge),t.closePath();break;case"triangle":c=s?s/2:g,t.moveTo(i+Math.sin(m)*c,n-Math.cos(m)*g),m+=ii,t.lineTo(i+Math.sin(m)*c,n-Math.cos(m)*g),m+=ii,t.lineTo(i+Math.sin(m)*c,n-Math.cos(m)*g),t.closePath();break;case"rectRounded":h=.516*g,l=g-h,r=Math.cos(m+ei)*l,d=Math.cos(m+ei)*(s?s/2-h:l),a=Math.sin(m+ei)*l,u=Math.sin(m+ei)*(s?s/2-h:l),t.arc(i-d,n-a,h,m-Ke,m-ti),t.arc(i+u,n-r,h,m-ti,m),t.arc(i+d,n+a,h,m,m+ti),t.arc(i-u,n+r,h,m+ti,m+Ke),t.closePath();break;case"rect":if(!p){l=Math.SQRT1_2*g,c=s?s/2:l,t.rect(i-c,n-l,2*c,2*l);break}m+=ei;case"rectRot":d=Math.cos(m)*(s?s/2:g),r=Math.cos(m)*g,a=Math.sin(m)*g,u=Math.sin(m)*(s?s/2:g),t.moveTo(i-d,n-a),t.lineTo(i+u,n-r),t.lineTo(i+d,n+a),t.lineTo(i-u,n+r),t.closePath();break;case"crossRot":m+=ei;case"cross":d=Math.cos(m)*(s?s/2:g),r=Math.cos(m)*g,a=Math.sin(m)*g,u=Math.sin(m)*(s?s/2:g),t.moveTo(i-d,n-a),t.lineTo(i+d,n+a),t.moveTo(i+u,n-r),t.lineTo(i-u,n+r);break;case"star":d=Math.cos(m)*(s?s/2:g),r=Math.cos(m)*g,a=Math.sin(m)*g,u=Math.sin(m)*(s?s/2:g),t.moveTo(i-d,n-a),t.lineTo(i+d,n+a),t.moveTo(i+u,n-r),t.lineTo(i-u,n+r),m+=ei,d=Math.cos(m)*(s?s/2:g),r=Math.cos(m)*g,a=Math.sin(m)*g,u=Math.sin(m)*(s?s/2:g),t.moveTo(i-d,n-a),t.lineTo(i+d,n+a),t.moveTo(i+u,n-r),t.lineTo(i-u,n+r);break;case"line":r=s?s/2:Math.cos(m)*g,a=Math.sin(m)*g,t.moveTo(i-r,n-a),t.lineTo(i+r,n+a);break;case"dash":t.moveTo(i,n),t.lineTo(i+Math.cos(m)*(s?s/2:g),n+Math.sin(m)*g);break;case!1:t.closePath()}t.fill(),e.borderWidth>0&&t.stroke()}}function nn(t,e,i){return i=i||.5,!e||t&&t.x>e.left-i&&t.x<e.right+i&&t.y>e.top-i&&t.y<e.bottom+i}function sn(t,e){t.save(),t.beginPath(),t.rect(e.left,e.top,e.right-e.left,e.bottom-e.top),t.clip()}function on(t){t.restore()}function rn(t,e,i,n,s){if(!e)return t.lineTo(i.x,i.y);if("middle"===s){const n=(e.x+i.x)/2;t.lineTo(n,e.y),t.lineTo(n,i.y)}else"after"===s!=!!n?t.lineTo(e.x,i.y):t.lineTo(i.x,e.y);t.lineTo(i.x,i.y)}function an(t,e,i,n){if(!e)return t.lineTo(i.x,i.y);t.bezierCurveTo(n?e.cp1x:e.cp2x,n?e.cp1y:e.cp2y,n?i.cp2x:i.cp1x,n?i.cp2y:i.cp1y,i.x,i.y)}function ln(t,e,i,n,s){if(s.strikethrough||s.underline){const o=t.measureText(n),r=e-o.actualBoundingBoxLeft,a=e+o.actualBoundingBoxRight,l=i-o.actualBoundingBoxAscent,h=i+o.actualBoundingBoxDescent,c=s.strikethrough?(l+h)/2:h;t.strokeStyle=t.fillStyle,t.beginPath(),t.lineWidth=s.decorationWidth||2,t.moveTo(r,c),t.lineTo(a,c),t.stroke()}}function hn(t,e){const i=t.fillStyle;t.fillStyle=e.color,t.fillRect(e.left,e.top,e.width,e.height),t.fillStyle=i}function cn(t,e,i,n,s,o={}){const r=Pe(e)?e:[e],a=o.strokeWidth>0&&""!==o.strokeColor;let l,h;for(t.save(),t.font=s.string,function(t,e){e.translation&&t.translate(e.translation[0],e.translation[1]),De(e.rotation)||t.rotate(e.rotation),e.color&&(t.fillStyle=e.color),e.textAlign&&(t.textAlign=e.textAlign),e.textBaseline&&(t.textBaseline=e.textBaseline)}(t,o),l=0;l<r.length;++l)h=r[l],o.backdrop&&hn(t,o.backdrop),a&&(o.strokeColor&&(t.strokeStyle=o.strokeColor),De(o.strokeWidth)||(t.lineWidth=o.strokeWidth),t.strokeText(h,i,n,o.maxWidth)),t.fillText(h,i,n,o.maxWidth),ln(t,i,n,h,o),n+=Number(s.lineHeight);t.restore()}function dn(t,e){const{x:i,y:n,w:s,h:o,radius:r}=e;t.arc(i+r.topLeft,n+r.topLeft,r.topLeft,1.5*Ke,Ke,!0),t.lineTo(i,n+o-r.bottomLeft),t.arc(i+r.bottomLeft,n+o-r.bottomLeft,r.bottomLeft,Ke,ti,!0),t.lineTo(i+s-r.bottomRight,n+o),t.arc(i+s-r.bottomRight,n+o-r.bottomRight,r.bottomRight,ti,0,!0),t.lineTo(i+s,n+r.topRight),t.arc(i+s-r.topRight,n+r.topRight,r.topRight,0,-ti,!0),t.lineTo(i+r.topLeft,n)}const un=/^(normal|(\d+(?:\.\d+)?)(px|em|%)?)$/,fn=/^(normal|italic|initial|inherit|unset|(oblique( -?[0-9]?[0-9]deg)?))$/;function pn(t,e){const i=(""+t).match(un);if(!i||"normal"===i[1])return 1.2*e;switch(t=+i[2],i[3]){case"px":return t;case"%":t/=100}return e*t}const gn=t=>+t||0;function mn(t,e){const i={},n=Ae(e),s=n?Object.keys(e):e,o=Ae(t)?n?i=>Ee(t[i],t[e[i]]):e=>t[e]:()=>t;for(const t of s)i[t]=gn(o(t));return i}function bn(t){return mn(t,{top:"y",right:"x",bottom:"y",left:"x"})}function xn(t){return mn(t,["topLeft","topRight","bottomLeft","bottomRight"])}function yn(t){const e=bn(t);return e.width=e.left+e.right,e.height=e.top+e.bottom,e}function vn(t,e){t=t||{},e=e||Ki.font;let i=Ee(t.size,e.size);"string"==typeof i&&(i=parseInt(i,10));let n=Ee(t.style,e.style);n&&!(""+n).match(fn)&&(console.warn('Invalid font style specified: "'+n+'"'),n=void 0);const s={family:Ee(t.family,e.family),lineHeight:pn(Ee(t.lineHeight,e.lineHeight),i),size:i,style:n,weight:Ee(t.weight,e.weight),string:""};return s.string=function(t){return!t||De(t.size)||De(t.family)?null:(t.style?t.style+" ":"")+(t.weight?t.weight+" ":"")+t.size+"px "+t.family}(s),s}function _n(t,e,i,n){let s,o,r,a=!0;for(s=0,o=t.length;s<o;++s)if(r=t[s],void 0!==r&&(void 0!==e&&"function"==typeof r&&(r=r(e),a=!1),void 0!==i&&Pe(r)&&(r=r[i%r.length],a=!1),void 0!==r))return n&&!a&&(n.cacheable=!1),r}function wn(t,e){return Object.assign(Object.create(t),e)}function Mn(t,e=[""],i,n,s=()=>t[0]){const o=i||t;void 0===n&&(n=Rn("_fallback",t));const r={[Symbol.toStringTag]:"Object",_cacheable:!0,_scopes:t,_rootScopes:o,_fallback:n,_getTarget:s,override:i=>Mn([i,...t],e,o,n)};return new Proxy(r,{deleteProperty(e,i){return delete e[i],delete e._keys,delete t[0][i],!0},get(i,n){return Pn(i,n,()=>function(t,e,i,n){let s;for(const o of e)if(s=Rn(On(o,t),i),void 0!==s)return Dn(t,s)?En(i,n,t,s):s}(n,e,t,i))},getOwnPropertyDescriptor(t,e){return Reflect.getOwnPropertyDescriptor(t._scopes[0],e)},getPrototypeOf(){return Reflect.getPrototypeOf(t[0])},has(t,e){return In(t).includes(e)},ownKeys(t){return In(t)},set(t,e,i){const n=t._storage||(t._storage=s());return t[e]=n[e]=i,delete t._keys,!0}})}function kn(t,e,i,n){const s={_cacheable:!1,_proxy:t,_context:e,_subProxy:i,_stack:new Set,_descriptors:Sn(t,n),setContext:e=>kn(t,e,i,n),override:s=>kn(t.override(s),e,i,n)};return new Proxy(s,{deleteProperty(e,i){return delete e[i],delete t[i],!0},get(t,e,i){return Pn(t,e,()=>function(t,e,i){const{_proxy:n,_context:s,_subProxy:o,_descriptors:r}=t;let a=n[e];return Xe(a)&&r.isScriptable(e)&&(a=function(t,e,i,n){const{_proxy:s,_context:o,_subProxy:r,_stack:a}=i;if(a.has(t))throw new Error("Recursion detected: "+Array.from(a).join("->")+"->"+t);a.add(t);let l=e(o,r||n);return a.delete(t),Dn(t,l)&&(l=En(s._scopes,s,t,l)),l}(e,a,t,i)),Pe(a)&&a.length&&(a=function(t,e,i,n){const{_proxy:s,_context:o,_subProxy:r,_descriptors:a}=i;if(void 0!==o.index&&n(t))return e[o.index%e.length];if(Ae(e[0])){const i=e,n=s._scopes.filter(t=>t!==i);e=[];for(const l of i){const i=En(n,s,t,l);e.push(kn(i,o,r&&r[t],a))}}return e}(e,a,t,r.isIndexable)),Dn(e,a)&&(a=kn(a,s,o&&o[e],r)),a}(t,e,i))},getOwnPropertyDescriptor(e,i){return e._descriptors.allKeys?Reflect.has(t,i)?{enumerable:!0,configurable:!0}:void 0:Reflect.getOwnPropertyDescriptor(t,i)},getPrototypeOf(){return Reflect.getPrototypeOf(t)},has(e,i){return Reflect.has(t,i)},ownKeys(){return Reflect.ownKeys(t)},set(e,i,n){return t[i]=n,delete e[i],!0}})}function Sn(t,e={scriptable:!0,indexable:!0}){const{_scriptable:i=e.scriptable,_indexable:n=e.indexable,_allKeys:s=e.allKeys}=t;return{allKeys:s,scriptable:i,indexable:n,isScriptable:Xe(i)?i:()=>i,isIndexable:Xe(n)?n:()=>n}}const On=(t,e)=>t?t+Ue(e):e,Dn=(t,e)=>Ae(e)&&"adapters"!==t&&(null===Object.getPrototypeOf(e)||e.constructor===Object);function Pn(t,e,i){if(Object.prototype.hasOwnProperty.call(t,e)||"constructor"===e)return t[e];const n=i();return t[e]=n,n}function An(t,e,i){return Xe(t)?t(e,i):t}const Cn=(t,e)=>!0===t?e:"string"==typeof t?$e(e,t):void 0;function Tn(t,e,i,n,s){for(const o of e){const e=Cn(i,o);if(e){t.add(e);const o=An(e._fallback,i,s);if(void 0!==o&&o!==i&&o!==n)return o}else if(!1===e&&void 0!==n&&i!==n)return null}return!1}function En(t,e,i,n){const s=e._rootScopes,o=An(e._fallback,i,n),r=[...t,...s],a=new Set;a.add(n);let l=Ln(a,r,i,o||i,n);return null!==l&&(void 0===o||o===i||(l=Ln(a,r,o,l,n),null!==l))&&Mn(Array.from(a),[""],s,o,()=>function(t,e,i){const n=t._getTarget();e in n||(n[e]={});const s=n[e];return Pe(s)&&Ae(i)?i:s||{}}(e,i,n))}function Ln(t,e,i,n,s){for(;i;)i=Tn(t,e,i,n,s);return i}function Rn(t,e){for(const i of e){if(!i)continue;const e=i[t];if(void 0!==e)return e}}function In(t){let e=t._keys;return e||(e=t._keys=function(t){const e=new Set;for(const i of t)for(const t of Object.keys(i).filter(t=>!t.startsWith("_")))e.add(t);return Array.from(e)}(t._scopes)),e}function Fn(t,e,i,n){const{iScale:s}=t,{key:o="r"}=this._parsing,r=new Array(n);let a,l,h,c;for(a=0,l=n;a<l;++a)h=a+i,c=e[h],r[a]={r:s.parse($e(c,o),h)};return r}const zn=Number.EPSILON||1e-14,Vn=(t,e)=>e<t.length&&!t[e].skip&&t[e],Bn=t=>"x"===t?"y":"x";function Wn(t,e,i,n){const s=t.skip?e:t,o=e,r=i.skip?e:i,a=fi(o,s),l=fi(r,o);let h=a/(a+l),c=l/(a+l);h=isNaN(h)?0:h,c=isNaN(c)?0:c;const d=n*h,u=n*c;return{previous:{x:o.x-d*(r.x-s.x),y:o.y-d*(r.y-s.y)},next:{x:o.x+u*(r.x-s.x),y:o.y+u*(r.y-s.y)}}}function jn(t,e,i){return Math.max(Math.min(t,i),e)}function Nn(t,e,i,n,s){let o,r,a,l;if(e.spanGaps&&(t=t.filter(t=>!t.skip)),"monotone"===e.cubicInterpolationMode)!function(t,e="x"){const i=Bn(e),n=t.length,s=Array(n).fill(0),o=Array(n);let r,a,l,h=Vn(t,0);for(r=0;r<n;++r)if(a=l,l=h,h=Vn(t,r+1),l){if(h){const t=h[e]-l[e];s[r]=0!==t?(h[i]-l[i])/t:0}o[r]=a?h?si(s[r-1])!==si(s[r])?0:(s[r-1]+s[r])/2:s[r-1]:s[r]}!function(t,e,i){const n=t.length;let s,o,r,a,l,h=Vn(t,0);for(let c=0;c<n-1;++c)l=h,h=Vn(t,c+1),l&&h&&(oi(e[c],0,zn)?i[c]=i[c+1]=0:(s=i[c]/e[c],o=i[c+1]/e[c],a=Math.pow(s,2)+Math.pow(o,2),a<=9||(r=3/Math.sqrt(a),i[c]=s*r*e[c],i[c+1]=o*r*e[c])))}(t,s,o),function(t,e,i="x"){const n=Bn(i),s=t.length;let o,r,a,l=Vn(t,0);for(let h=0;h<s;++h){if(r=a,a=l,l=Vn(t,h+1),!a)continue;const s=a[i],c=a[n];r&&(o=(s-r[i])/3,a[`cp1${i}`]=s-o,a[`cp1${n}`]=c-o*e[h]),l&&(o=(l[i]-s)/3,a[`cp2${i}`]=s+o,a[`cp2${n}`]=c+o*e[h])}}(t,o,e)}(t,s);else{let i=n?t[t.length-1]:t[0];for(o=0,r=t.length;o<r;++o)a=t[o],l=Wn(i,a,t[Math.min(o+1,r-(n?0:1))%r],e.tension),a.cp1x=l.previous.x,a.cp1y=l.previous.y,a.cp2x=l.next.x,a.cp2y=l.next.y,i=a}e.capBezierPoints&&function(t,e){let i,n,s,o,r,a=nn(t[0],e);for(i=0,n=t.length;i<n;++i)r=o,o=a,a=i<n-1&&nn(t[i+1],e),o&&(s=t[i],r&&(s.cp1x=jn(s.cp1x,e.left,e.right),s.cp1y=jn(s.cp1y,e.top,e.bottom)),a&&(s.cp2x=jn(s.cp2x,e.left,e.right),s.cp2y=jn(s.cp2y,e.top,e.bottom)))}(t,i)}function Hn(){return"undefined"!=typeof window&&"undefined"!=typeof document}function $n(t){let e=t.parentNode;return e&&"[object ShadowRoot]"===e.toString()&&(e=e.host),e}function Un(t,e,i){let n;return"string"==typeof t?(n=parseInt(t,10),-1!==t.indexOf("%")&&(n=n/100*e.parentNode[i])):n=t,n}const Yn=t=>t.ownerDocument.defaultView.getComputedStyle(t,null),Xn=["top","right","bottom","left"];function qn(t,e,i){const n={};i=i?"-"+i:"";for(let s=0;s<4;s++){const o=Xn[s];n[o]=parseFloat(t[e+"-"+o+i])||0}return n.width=n.left+n.right,n.height=n.top+n.bottom,n}function Kn(t,e){if("native"in t)return t;const{canvas:i,currentDevicePixelRatio:n}=e,s=Yn(i),o="border-box"===s.boxSizing,r=qn(s,"padding"),a=qn(s,"border","width"),{x:l,y:h,box:c}=function(t,e){const i=t.touches,n=i&&i.length?i[0]:t,{offsetX:s,offsetY:o}=n;let r,a,l=!1;if(((t,e,i)=>(t>0||e>0)&&(!i||!i.shadowRoot))(s,o,t.target))r=s,a=o;else{const t=e.getBoundingClientRect();r=n.clientX-t.left,a=n.clientY-t.top,l=!0}return{x:r,y:a,box:l}}(t,i),d=r.left+(c&&a.left),u=r.top+(c&&a.top);let{width:f,height:p}=e;return o&&(f-=r.width+a.width,p-=r.height+a.height),{x:Math.round((l-d)/f*i.width/n),y:Math.round((h-u)/p*i.height/n)}}const Gn=t=>Math.round(10*t)/10;function Jn(t,e,i){const n=e||1,s=Math.floor(t.height*n),o=Math.floor(t.width*n);t.height=Math.floor(t.height),t.width=Math.floor(t.width);const r=t.canvas;return r.style&&(i||!r.style.height&&!r.style.width)&&(r.style.height=`${t.height}px`,r.style.width=`${t.width}px`),(t.currentDevicePixelRatio!==n||r.height!==s||r.width!==o)&&(t.currentDevicePixelRatio=n,r.height=s,r.width=o,t.ctx.setTransform(n,0,0,n,0,0),!0)}const Zn=function(){let t=!1;try{const e={get passive(){return t=!0,!1}};Hn()&&(window.addEventListener("test",null,e),window.removeEventListener("test",null,e))}catch(t){}return t}();function Qn(t,e){const i=function(t,e){return Yn(t).getPropertyValue(e)}(t,e),n=i&&i.match(/^(\d+)(\.\d+)?px$/);return n?+n[1]:void 0}function ts(t,e,i,n){return{x:t.x+i*(e.x-t.x),y:t.y+i*(e.y-t.y)}}function es(t,e,i,n){return{x:t.x+i*(e.x-t.x),y:"middle"===n?i<.5?t.y:e.y:"after"===n?i<1?t.y:e.y:i>0?e.y:t.y}}function is(t,e,i,n){const s={x:t.cp2x,y:t.cp2y},o={x:e.cp1x,y:e.cp1y},r=ts(t,s,i),a=ts(s,o,i),l=ts(o,e,i),h=ts(r,a,i),c=ts(a,l,i);return ts(h,c,i)}function ns(t,e,i){return t?function(t,e){return{x(i){return t+t+e-i},setWidth(t){e=t},textAlign(t){return"center"===t?t:"right"===t?"left":"right"},xPlus(t,e){return t-e},leftForLtr(t,e){return t-e}}}(e,i):{x(t){return t},setWidth(t){},textAlign(t){return t},xPlus(t,e){return t+e},leftForLtr(t,e){return t}}}function ss(t,e){let i,n;"ltr"!==e&&"rtl"!==e||(i=t.canvas.style,n=[i.getPropertyValue("direction"),i.getPropertyPriority("direction")],i.setProperty("direction",e,"important"),t.prevTextDirection=n)}function os(t,e){void 0!==e&&(delete t.prevTextDirection,t.canvas.style.setProperty("direction",e[0],e[1]))}function rs(t){return"angle"===t?{between:mi,compare:pi,normalize:gi}:{between:xi,compare:(t,e)=>t-e,normalize:t=>t}}function as({start:t,end:e,count:i,loop:n,style:s}){return{start:t%i,end:e%i,loop:n&&(e-t+1)%i==0,style:s}}function ls(t,e,i){if(!i)return[t];const{property:n,start:s,end:o}=i,r=e.length,{compare:a,between:l,normalize:h}=rs(n),{start:c,end:d,loop:u,style:f}=function(t,e,i){const{property:n,start:s,end:o}=i,{between:r,normalize:a}=rs(n),l=e.length;let h,c,{start:d,end:u,loop:f}=t;if(f){for(d+=l,u+=l,h=0,c=l;h<c&&r(a(e[d%l][n]),s,o);++h)d--,u--;d%=l,u%=l}return u<d&&(u+=l),{start:d,end:u,loop:f,style:t.style}}(t,e,i),p=[];let g,m,b,x=!1,y=null;const v=()=>x||l(s,b,g)&&0!==a(s,b),_=()=>!x||0===a(o,g)||l(o,b,g);for(let t=c,i=c;t<=d;++t)m=e[t%r],m.skip||(g=h(m[n]),g!==b&&(x=l(g,s,o),null===y&&v()&&(y=0===a(g,s)?t:i),null!==y&&_()&&(p.push(as({start:y,end:t,loop:u,count:r,style:f})),y=null),i=t,b=g));return null!==y&&p.push(as({start:y,end:d,loop:u,count:r,style:f})),p}function hs(t,e){const i=[],n=t.segments;for(let s=0;s<n.length;s++){const o=ls(n[s],t.points,e);o.length&&i.push(...o)}return i}function cs(t){return{backgroundColor:t.backgroundColor,borderCapStyle:t.borderCapStyle,borderDash:t.borderDash,borderDashOffset:t.borderDashOffset,borderJoinStyle:t.borderJoinStyle,borderWidth:t.borderWidth,borderColor:t.borderColor}}function ds(t,e){if(!e)return!1;const i=[],n=function(t,e){return Ii(e)?(i.includes(e)||i.push(e),i.indexOf(e)):e};return JSON.stringify(t,n)!==JSON.stringify(e,n)}function us(t,e,i){return t.options.clip?t[i]:e[i]}function fs(t,e){const i=e._clip;if(i.disabled)return!1;const n=function(t,e){const{xScale:i,yScale:n}=t;return i&&n?{left:us(i,e,"left"),right:us(i,e,"right"),top:us(n,e,"top"),bottom:us(n,e,"bottom")}:e}(e,t.chartArea);return{left:!1===i.left?0:n.left-(!0===i.left?0:i.left),right:!1===i.right?t.width:n.right+(!0===i.right?0:i.right),top:!1===i.top?0:n.top-(!0===i.top?0:i.top),bottom:!1===i.bottom?t.height:n.bottom+(!0===i.bottom?0:i.bottom)}}class ps{constructor(){this._request=null,this._charts=new Map,this._running=!1,this._lastDate=void 0}_notify(t,e,i,n){const s=e.listeners[n],o=e.duration;s.forEach(n=>n({chart:t,initial:e.initial,numSteps:o,currentStep:Math.min(i-e.start,o)}))}_refresh(){this._request||(this._running=!0,this._request=Si.call(window,()=>{this._update(),this._request=null,this._running&&this._refresh()}))}_update(t=Date.now()){let e=0;this._charts.forEach((i,n)=>{if(!i.running||!i.items.length)return;const s=i.items;let o,r=s.length-1,a=!1;for(;r>=0;--r)o=s[r],o._active?(o._total>i.duration&&(i.duration=o._total),o.tick(t),a=!0):(s[r]=s[s.length-1],s.pop());a&&(n.draw(),this._notify(n,i,t,"progress")),s.length||(i.running=!1,this._notify(n,i,t,"complete"),i.initial=!1),e+=s.length}),this._lastDate=t,0===e&&(this._running=!1)}_getAnims(t){const e=this._charts;let i=e.get(t);return i||(i={running:!1,initial:!0,items:[],listeners:{complete:[],progress:[]}},e.set(t,i)),i}listen(t,e,i){this._getAnims(t).listeners[e].push(i)}add(t,e){e&&e.length&&this._getAnims(t).items.push(...e)}has(t){return this._getAnims(t).items.length>0}start(t){const e=this._charts.get(t);e&&(e.running=!0,e.start=Date.now(),e.duration=e.items.reduce((t,e)=>Math.max(t,e._duration),0),this._refresh())}running(t){if(!this._running)return!1;const e=this._charts.get(t);return!!(e&&e.running&&e.items.length)}stop(t){const e=this._charts.get(t);if(!e||!e.items.length)return;const i=e.items;let n=i.length-1;for(;n>=0;--n)i[n].cancel();e.items=[],this._notify(t,e,Date.now(),"complete")}remove(t){return this._charts.delete(t)}}var gs=new ps;const ms="transparent",bs={boolean(t,e,i){return i>.5?e:t},color(t,e,i){const n=Fi(t||ms),s=n.valid&&Fi(e||ms);return s&&s.valid?s.mix(n,i).hexString():e},number(t,e,i){return t+(e-t)*i}};class xs{constructor(t,e,i,n){const s=e[i];n=_n([t.to,n,s,t.from]);const o=_n([t.from,s,n]);this._active=!0,this._fn=t.fn||bs[t.type||typeof o],this._easing=Ri[t.easing]||Ri.linear,this._start=Math.floor(Date.now()+(t.delay||0)),this._duration=this._total=Math.floor(t.duration),this._loop=!!t.loop,this._target=e,this._prop=i,this._from=o,this._to=n,this._promises=void 0}active(){return this._active}update(t,e,i){if(this._active){this._notify(!1);const n=this._target[this._prop],s=i-this._start,o=this._duration-s;this._start=i,this._duration=Math.floor(Math.max(o,t.duration)),this._total+=s,this._loop=!!t.loop,this._to=_n([t.to,e,n,t.from]),this._from=_n([t.from,n,e])}}cancel(){this._active&&(this.tick(Date.now()),this._active=!1,this._notify(!1))}tick(t){const e=t-this._start,i=this._duration,n=this._prop,s=this._from,o=this._loop,r=this._to;let a;if(this._active=s!==r&&(o||e<i),!this._active)return this._target[n]=r,void this._notify(!0);e<0?this._target[n]=s:(a=e/i%2,a=o&&a>1?2-a:a,a=this._easing(Math.min(1,Math.max(0,a))),this._target[n]=this._fn(s,r,a))}wait(){const t=this._promises||(this._promises=[]);return new Promise((e,i)=>{t.push({res:e,rej:i})})}_notify(t){const e=t?"res":"rej",i=this._promises||[];for(let t=0;t<i.length;t++)i[t][e]()}}class ys{constructor(t,e){this._chart=t,this._properties=new Map,this.configure(e)}configure(t){if(!Ae(t))return;const e=Object.keys(Ki.animation),i=this._properties;Object.getOwnPropertyNames(t).forEach(n=>{const s=t[n];if(!Ae(s))return;const o={};for(const t of e)o[t]=s[t];(Pe(s.properties)&&s.properties||[n]).forEach(t=>{t!==n&&i.has(t)||i.set(t,o)})})}_animateOptions(t,e){const i=e.options,n=function(t,e){if(!e)return;let i=t.options;if(i)return i.$shared&&(t.options=i=Object.assign({},i,{$shared:!1,$animations:{}})),i;t.options=e}(t,i);if(!n)return[];const s=this._createAnimations(n,i);return i.$shared&&function(t,e){const i=[],n=Object.keys(e);for(let e=0;e<n.length;e++){const s=t[n[e]];s&&s.active()&&i.push(s.wait())}return Promise.all(i)}(t.options.$animations,i).then(()=>{t.options=i},()=>{}),s}_createAnimations(t,e){const i=this._properties,n=[],s=t.$animations||(t.$animations={}),o=Object.keys(e),r=Date.now();let a;for(a=o.length-1;a>=0;--a){const l=o[a];if("$"===l.charAt(0))continue;if("options"===l){n.push(...this._animateOptions(t,e));continue}const h=e[l];let c=s[l];const d=i.get(l);if(c){if(d&&c.active()){c.update(d,h,r);continue}c.cancel()}d&&d.duration?(s[l]=c=new xs(d,t,l,h),n.push(c)):t[l]=h}return n}update(t,e){if(0===this._properties.size)return void Object.assign(t,e);const i=this._createAnimations(t,e);return i.length?(gs.add(this._chart,i),!0):void 0}}function vs(t,e){const i=t&&t.options||{},n=i.reverse,s=void 0===i.min?e:0,o=void 0===i.max?e:0;return{start:n?o:s,end:n?s:o}}function _s(t,e){const i=[],n=t._getSortedDatasetMetas(e);let s,o;for(s=0,o=n.length;s<o;++s)i.push(n[s].index);return i}function ws(t,e,i,n={}){const s=t.keys,o="single"===n.mode;let r,a,l,h;if(null===e)return;let c=!1;for(r=0,a=s.length;r<a;++r){if(l=+s[r],l===i){if(c=!0,n.all)continue;break}h=t.values[l],Ce(h)&&(o||0===e||si(e)===si(h))&&(e+=h)}return c||n.all?e:0}function Ms(t,e){const i=t&&t.options.stacked;return i||void 0===i&&void 0!==e.stack}function ks(t,e,i){const n=t[e]||(t[e]={});return n[i]||(n[i]={})}function Ss(t,e,i,n){for(const s of e.getMatchingVisibleMetas(n).reverse()){const e=t[s.index];if(i&&e>0||!i&&e<0)return s.index}return null}function Os(t,e){const{chart:i,_cachedMeta:n}=t,s=i._stacks||(i._stacks={}),{iScale:o,vScale:r,index:a}=n,l=o.axis,h=r.axis,c=function(t,e,i){return`${t.id}.${e.id}.${i.stack||i.type}`}(o,r,n),d=e.length;let u;for(let t=0;t<d;++t){const i=e[t],{[l]:o,[h]:d}=i;u=(i._stacks||(i._stacks={}))[h]=ks(s,c,o),u[a]=d,u._top=Ss(u,r,!0,n.type),u._bottom=Ss(u,r,!1,n.type),(u._visualValues||(u._visualValues={}))[a]=d}}function Ds(t,e){const i=t.scales;return Object.keys(i).filter(t=>i[t].axis===e).shift()}function Ps(t,e){const i=t.controller.index,n=t.vScale&&t.vScale.axis;if(n){e=e||t._parsed;for(const t of e){const e=t._stacks;if(!e||void 0===e[n]||void 0===e[n][i])return;delete e[n][i],void 0!==e[n]._visualValues&&void 0!==e[n]._visualValues[i]&&delete e[n]._visualValues[i]}}}const As=t=>"reset"===t||"none"===t,Cs=(t,e)=>e?t:Object.assign({},t);class Ts{static defaults={};static datasetElementType=null;static dataElementType=null;constructor(t,e){this.chart=t,this._ctx=t.ctx,this.index=e,this._cachedDataOpts={},this._cachedMeta=this.getMeta(),this._type=this._cachedMeta.type,this.options=void 0,this._parsing=!1,this._data=void 0,this._objectData=void 0,this._sharedOptions=void 0,this._drawStart=void 0,this._drawCount=void 0,this.enableOptionSharing=!1,this.supportsDecimation=!1,this.$context=void 0,this._syncList=[],this.datasetElementType=new.target.datasetElementType,this.dataElementType=new.target.dataElementType,this.initialize()}initialize(){const t=this._cachedMeta;this.configure(),this.linkScales(),t._stacked=Ms(t.vScale,t),this.addElements(),this.options.fill&&!this.chart.isPluginEnabled("filler")&&console.warn("Tried to use the 'fill' option without the 'Filler' plugin enabled. Please import and register the 'Filler' plugin and make sure it is not disabled in the options")}updateIndex(t){this.index!==t&&Ps(this._cachedMeta),this.index=t}linkScales(){const t=this.chart,e=this._cachedMeta,i=this.getDataset(),n=(t,e,i,n)=>"x"===t?e:"r"===t?n:i,s=e.xAxisID=Ee(i.xAxisID,Ds(t,"x")),o=e.yAxisID=Ee(i.yAxisID,Ds(t,"y")),r=e.rAxisID=Ee(i.rAxisID,Ds(t,"r")),a=e.indexAxis,l=e.iAxisID=n(a,s,o,r),h=e.vAxisID=n(a,o,s,r);e.xScale=this.getScaleForId(s),e.yScale=this.getScaleForId(o),e.rScale=this.getScaleForId(r),e.iScale=this.getScaleForId(l),e.vScale=this.getScaleForId(h)}getDataset(){return this.chart.data.datasets[this.index]}getMeta(){return this.chart.getDatasetMeta(this.index)}getScaleForId(t){return this.chart.scales[t]}_getOtherScale(t){const e=this._cachedMeta;return t===e.iScale?e.vScale:e.iScale}reset(){this._update("reset")}_destroy(){const t=this._cachedMeta;this._data&&Mi(this._data,this),t._stacked&&Ps(t)}_dataCheck(){const t=this.getDataset(),e=t.data||(t.data=[]),i=this._data;if(Ae(e)){const t=this._cachedMeta;this._data=function(t,e){const{iScale:i,vScale:n}=e,s="x"===i.axis?"x":"y",o="x"===n.axis?"x":"y",r=Object.keys(t),a=new Array(r.length);let l,h,c;for(l=0,h=r.length;l<h;++l)c=r[l],a[l]={[s]:c,[o]:t[c]};return a}(e,t)}else if(i!==e){if(i){Mi(i,this);const t=this._cachedMeta;Ps(t),t._parsed=[]}e&&Object.isExtensible(e)&&((n=e)._chartjs?n._chartjs.listeners.push(this):(Object.defineProperty(n,"_chartjs",{configurable:!0,enumerable:!1,value:{listeners:[this]}}),wi.forEach(t=>{const e="_onData"+Ue(t),i=n[t];Object.defineProperty(n,t,{configurable:!0,enumerable:!1,value(...t){const s=i.apply(this,t);return n._chartjs.listeners.forEach(i=>{"function"==typeof i[e]&&i[e](...t)}),s}})}))),this._syncList=[],this._data=e}var n}addElements(){const t=this._cachedMeta;this._dataCheck(),this.datasetElementType&&(t.dataset=new this.datasetElementType)}buildOrUpdateElements(t){const e=this._cachedMeta,i=this.getDataset();let n=!1;this._dataCheck();const s=e._stacked;e._stacked=Ms(e.vScale,e),e.stack!==i.stack&&(n=!0,Ps(e),e.stack=i.stack),this._resyncElements(t),(n||s!==e._stacked)&&(Os(this,e._parsed),e._stacked=Ms(e.vScale,e))}configure(){const t=this.chart.config,e=t.datasetScopeKeys(this._type),i=t.getOptionScopes(this.getDataset(),e,!0);this.options=t.createResolver(i,this.getContext()),this._parsing=this.options.parsing,this._cachedDataOpts={}}parse(t,e){const{_cachedMeta:i,_data:n}=this,{iScale:s,_stacked:o}=i,r=s.axis;let a,l,h,c=0===t&&e===n.length||i._sorted,d=t>0&&i._parsed[t-1];if(!1===this._parsing)i._parsed=n,i._sorted=!0,h=n;else{h=Pe(n[t])?this.parseArrayData(i,n,t,e):Ae(n[t])?this.parseObjectData(i,n,t,e):this.parsePrimitiveData(i,n,t,e);const s=()=>null===l[r]||d&&l[r]<d[r];for(a=0;a<e;++a)i._parsed[a+t]=l=h[a],c&&(s()&&(c=!1),d=l);i._sorted=c}o&&Os(this,h)}parsePrimitiveData(t,e,i,n){const{iScale:s,vScale:o}=t,r=s.axis,a=o.axis,l=s.getLabels(),h=s===o,c=new Array(n);let d,u,f;for(d=0,u=n;d<u;++d)f=d+i,c[d]={[r]:h||s.parse(l[f],f),[a]:o.parse(e[f],f)};return c}parseArrayData(t,e,i,n){const{xScale:s,yScale:o}=t,r=new Array(n);let a,l,h,c;for(a=0,l=n;a<l;++a)h=a+i,c=e[h],r[a]={x:s.parse(c[0],h),y:o.parse(c[1],h)};return r}parseObjectData(t,e,i,n){const{xScale:s,yScale:o}=t,{xAxisKey:r="x",yAxisKey:a="y"}=this._parsing,l=new Array(n);let h,c,d,u;for(h=0,c=n;h<c;++h)d=h+i,u=e[d],l[h]={x:s.parse($e(u,r),d),y:o.parse($e(u,a),d)};return l}getParsed(t){return this._cachedMeta._parsed[t]}getDataElement(t){return this._cachedMeta.data[t]}applyStack(t,e,i){const n=this.chart,s=this._cachedMeta,o=e[t.axis];return ws({keys:_s(n,!0),values:e._stacks[t.axis]._visualValues},o,s.index,{mode:i})}updateRangeFromParsed(t,e,i,n){const s=i[e.axis];let o=null===s?NaN:s;const r=n&&i._stacks[e.axis];n&&r&&(n.values=r,o=ws(n,s,this._cachedMeta.index)),t.min=Math.min(t.min,o),t.max=Math.max(t.max,o)}getMinMax(t,e){const i=this._cachedMeta,n=i._parsed,s=i._sorted&&t===i.iScale,o=n.length,r=this._getOtherScale(t),a=((t,e,i)=>t&&!e.hidden&&e._stacked&&{keys:_s(i,!0),values:null})(e,i,this.chart),l={min:Number.POSITIVE_INFINITY,max:Number.NEGATIVE_INFINITY},{min:h,max:c}=function(t){const{min:e,max:i,minDefined:n,maxDefined:s}=t.getUserBounds();return{min:n?e:Number.NEGATIVE_INFINITY,max:s?i:Number.POSITIVE_INFINITY}}(r);let d,u;function f(){u=n[d];const e=u[r.axis];return!Ce(u[t.axis])||h>e||c<e}for(d=0;d<o&&(f()||(this.updateRangeFromParsed(l,t,u,a),!s));++d);if(s)for(d=o-1;d>=0;--d)if(!f()){this.updateRangeFromParsed(l,t,u,a);break}return l}getAllParsedValues(t){const e=this._cachedMeta._parsed,i=[];let n,s,o;for(n=0,s=e.length;n<s;++n)o=e[n][t.axis],Ce(o)&&i.push(o);return i}getMaxOverflow(){return!1}getLabelAndValue(t){const e=this._cachedMeta,i=e.iScale,n=e.vScale,s=this.getParsed(t);return{label:i?""+i.getLabelForValue(s[i.axis]):"",value:n?""+n.getLabelForValue(s[n.axis]):""}}_update(t){const e=this._cachedMeta;this.update(t||"default"),e._clip=function(t){let e,i,n,s;return Ae(t)?(e=t.top,i=t.right,n=t.bottom,s=t.left):e=i=n=s=t,{top:e,right:i,bottom:n,left:s,disabled:!1===t}}(Ee(this.options.clip,function(t,e,i){if(!1===i)return!1;const n=vs(t,i),s=vs(e,i);return{top:s.end,right:n.end,bottom:s.start,left:n.start}}(e.xScale,e.yScale,this.getMaxOverflow())))}update(t){}draw(){const t=this._ctx,e=this.chart,i=this._cachedMeta,n=i.data||[],s=e.chartArea,o=[],r=this._drawStart||0,a=this._drawCount||n.length-r,l=this.options.drawActiveElementsOnTop;let h;for(i.dataset&&i.dataset.draw(t,s,r,a),h=r;h<r+a;++h){const e=n[h];e.hidden||(e.active&&l?o.push(e):e.draw(t,s))}for(h=0;h<o.length;++h)o[h].draw(t,s)}getStyle(t,e){const i=e?"active":"default";return void 0===t&&this._cachedMeta.dataset?this.resolveDatasetElementOptions(i):this.resolveDataElementOptions(t||0,i)}getContext(t,e,i){const n=this.getDataset();let s;if(t>=0&&t<this._cachedMeta.data.length){const e=this._cachedMeta.data[t];s=e.$context||(e.$context=function(t,e,i){return wn(t,{active:!1,dataIndex:e,parsed:void 0,raw:void 0,element:i,index:e,mode:"default",type:"data"})}(this.getContext(),t,e)),s.parsed=this.getParsed(t),s.raw=n.data[t],s.index=s.dataIndex=t}else s=this.$context||(this.$context=function(t,e){return wn(t,{active:!1,dataset:void 0,datasetIndex:e,index:e,mode:"default",type:"dataset"})}(this.chart.getContext(),this.index)),s.dataset=n,s.index=s.datasetIndex=this.index;return s.active=!!e,s.mode=i,s}resolveDatasetElementOptions(t){return this._resolveElementOptions(this.datasetElementType.id,t)}resolveDataElementOptions(t,e){return this._resolveElementOptions(this.dataElementType.id,e,t)}_resolveElementOptions(t,e="default",i){const n="active"===e,s=this._cachedDataOpts,o=t+"-"+e,r=s[o],a=this.enableOptionSharing&&Ye(i);if(r)return Cs(r,a);const l=this.chart.config,h=l.datasetElementScopeKeys(this._type,t),c=n?[`${t}Hover`,"hover",t,""]:[t,""],d=l.getOptionScopes(this.getDataset(),h),u=Object.keys(Ki.elements[t]),f=l.resolveNamedOptions(d,u,()=>this.getContext(i,n,e),c);return f.$shared&&(f.$shared=a,s[o]=Object.freeze(Cs(f,a))),f}_resolveAnimations(t,e,i){const n=this.chart,s=this._cachedDataOpts,o=`animation-${e}`,r=s[o];if(r)return r;let a;if(!1!==n.options.animation){const n=this.chart.config,s=n.datasetAnimationScopeKeys(this._type,e),o=n.getOptionScopes(this.getDataset(),s);a=n.createResolver(o,this.getContext(t,i,e))}const l=new ys(n,a&&a.animations);return a&&a._cacheable&&(s[o]=Object.freeze(l)),l}getSharedOptions(t){if(t.$shared)return this._sharedOptions||(this._sharedOptions=Object.assign({},t))}includeOptions(t,e){return!e||As(t)||this.chart._animationsDisabled}_getSharedOptions(t,e){const i=this.resolveDataElementOptions(t,e),n=this._sharedOptions,s=this.getSharedOptions(i),o=this.includeOptions(e,s)||s!==n;return this.updateSharedOptions(s,e,i),{sharedOptions:s,includeOptions:o}}updateElement(t,e,i,n){As(n)?Object.assign(t,i):this._resolveAnimations(e,n).update(t,i)}updateSharedOptions(t,e,i){t&&!As(e)&&this._resolveAnimations(void 0,e).update(t,i)}_setStyle(t,e,i,n){t.active=n;const s=this.getStyle(e,n);this._resolveAnimations(e,i,n).update(t,{options:!n&&this.getSharedOptions(s)||s})}removeHoverStyle(t,e,i){this._setStyle(t,i,"active",!1)}setHoverStyle(t,e,i){this._setStyle(t,i,"active",!0)}_removeDatasetHoverStyle(){const t=this._cachedMeta.dataset;t&&this._setStyle(t,void 0,"active",!1)}_setDatasetHoverStyle(){const t=this._cachedMeta.dataset;t&&this._setStyle(t,void 0,"active",!0)}_resyncElements(t){const e=this._data,i=this._cachedMeta.data;for(const[t,e,i]of this._syncList)this[t](e,i);this._syncList=[];const n=i.length,s=e.length,o=Math.min(s,n);o&&this.parse(0,o),s>n?this._insertElements(n,s-n,t):s<n&&this._removeElements(s,n-s)}_insertElements(t,e,i=!0){const n=this._cachedMeta,s=n.data,o=t+e;let r;const a=t=>{for(t.length+=e,r=t.length-1;r>=o;r--)t[r]=t[r-e]};for(a(s),r=t;r<o;++r)s[r]=new this.dataElementType;this._parsing&&a(n._parsed),this.parse(t,e),i&&this.updateElements(s,t,e,"reset")}updateElements(t,e,i,n){}_removeElements(t,e){const i=this._cachedMeta;if(this._parsing){const n=i._parsed.splice(t,e);i._stacked&&Ps(i,n)}i.data.splice(t,e)}_sync(t){if(this._parsing)this._syncList.push(t);else{const[e,i,n]=t;this[e](i,n)}this.chart._dataChanges.push([this.index,...t])}_onDataPush(){const t=arguments.length;this._sync(["_insertElements",this.getDataset().data.length-t,t])}_onDataPop(){this._sync(["_removeElements",this._cachedMeta.data.length-1,1])}_onDataShift(){this._sync(["_removeElements",0,1])}_onDataSplice(t,e){e&&this._sync(["_removeElements",t,e]);const i=arguments.length-2;i&&this._sync(["_insertElements",t,i])}_onDataUnshift(){this._sync(["_insertElements",0,arguments.length])}}function Es(t){const e=t.iScale,i=function(t,e){if(!t._cache.$bar){const i=t.getMatchingVisibleMetas(e);let n=[];for(let e=0,s=i.length;e<s;e++)n=n.concat(i[e].controller.getAllParsedValues(t));t._cache.$bar=ki(n.sort((t,e)=>t-e))}return t._cache.$bar}(e,t.type);let n,s,o,r,a=e._length;const l=()=>{32767!==o&&-32768!==o&&(Ye(r)&&(a=Math.min(a,Math.abs(o-r)||a)),r=o)};for(n=0,s=i.length;n<s;++n)o=e.getPixelForValue(i[n]),l();for(r=void 0,n=0,s=e.ticks.length;n<s;++n)o=e.getPixelForTick(n),l();return a}function Ls(t,e,i,n){return Pe(t)?function(t,e,i,n){const s=i.parse(t[0],n),o=i.parse(t[1],n),r=Math.min(s,o),a=Math.max(s,o);let l=r,h=a;Math.abs(r)>Math.abs(a)&&(l=a,h=r),e[i.axis]=h,e._custom={barStart:l,barEnd:h,start:s,end:o,min:r,max:a}}(t,e,i,n):e[i.axis]=i.parse(t,n),e}function Rs(t,e,i,n){const s=t.iScale,o=t.vScale,r=s.getLabels(),a=s===o,l=[];let h,c,d,u;for(h=i,c=i+n;h<c;++h)u=e[h],d={},d[s.axis]=a||s.parse(r[h],h),l.push(Ls(u,d,o,h));return l}function Is(t){return t&&void 0!==t.barStart&&void 0!==t.barEnd}function Fs(t,e,i,n){let s=e.borderSkipped;const o={};if(!s)return void(t.borderSkipped=o);if(!0===s)return void(t.borderSkipped={top:!0,right:!0,bottom:!0,left:!0});const{start:r,end:a,reverse:l,top:h,bottom:c}=function(t){let e,i,n,s,o;return t.horizontal?(e=t.base>t.x,i="left",n="right"):(e=t.base<t.y,i="bottom",n="top"),e?(s="end",o="start"):(s="start",o="end"),{start:i,end:n,reverse:e,top:s,bottom:o}}(t);"middle"===s&&i&&(t.enableBorderRadius=!0,(i._top||0)===n?s=h:(i._bottom||0)===n?s=c:(o[zs(c,r,a,l)]=!0,s=h)),o[zs(s,r,a,l)]=!0,t.borderSkipped=o}function zs(t,e,i,n){var s,o,r;return n?(r=i,t=Vs(t=(s=t)===(o=e)?r:s===r?o:s,i,e)):t=Vs(t,e,i),t}function Vs(t,e,i){return"start"===t?e:"end"===t?i:t}function Bs(t,{inflateAmount:e},i){t.inflateAmount="auto"===e?1===i?.33:0:e}class Ws extends Ts{static id="doughnut";static defaults={datasetElementType:!1,dataElementType:"arc",animation:{animateRotate:!0,animateScale:!1},animations:{numbers:{type:"number",properties:["circumference","endAngle","innerRadius","outerRadius","startAngle","x","y","offset","borderWidth","spacing"]}},cutout:"50%",rotation:0,circumference:360,radius:"100%",spacing:0,indexAxis:"r"};static descriptors={_scriptable:t=>"spacing"!==t,_indexable:t=>"spacing"!==t&&!t.startsWith("borderDash")&&!t.startsWith("hoverBorderDash")};static overrides={aspectRatio:1,plugins:{legend:{labels:{generateLabels(t){const e=t.data;if(e.labels.length&&e.datasets.length){const{labels:{pointStyle:i,color:n}}=t.legend.options;return e.labels.map((e,s)=>{const o=t.getDatasetMeta(0).controller.getStyle(s);return{text:e,fillStyle:o.backgroundColor,strokeStyle:o.borderColor,fontColor:n,lineWidth:o.borderWidth,pointStyle:i,hidden:!t.getDataVisibility(s),index:s}})}return[]}},onClick(t,e,i){i.chart.toggleDataVisibility(e.index),i.chart.update()}}}};constructor(t,e){super(t,e),this.enableOptionSharing=!0,this.innerRadius=void 0,this.outerRadius=void 0,this.offsetX=void 0,this.offsetY=void 0}linkScales(){}parse(t,e){const i=this.getDataset().data,n=this._cachedMeta;if(!1===this._parsing)n._parsed=i;else{let s,o,r=t=>+i[t];if(Ae(i[t])){const{key:t="value"}=this._parsing;r=e=>+$e(i[e],t)}for(s=t,o=t+e;s<o;++s)n._parsed[s]=r(s)}}_getRotation(){return hi(this.options.rotation-90)}_getCircumference(){return hi(this.options.circumference)}_getRotationExtents(){let t=Ge,e=-Ge;for(let i=0;i<this.chart.data.datasets.length;++i)if(this.chart.isDatasetVisible(i)&&this.chart.getDatasetMeta(i).type===this._type){const n=this.chart.getDatasetMeta(i).controller,s=n._getRotation(),o=n._getCircumference();t=Math.min(t,s),e=Math.max(e,s+o)}return{rotation:t,circumference:e-t}}update(t){const e=this.chart,{chartArea:i}=e,n=this._cachedMeta,s=n.data,o=this.getMaxBorderWidth()+this.getMaxOffset(s)+this.options.spacing,r=Math.max((Math.min(i.width,i.height)-o)/2,0),a=Math.min((h=r,"string"==typeof(l=this.options.cutout)&&l.endsWith("%")?parseFloat(l)/100:+l/h),1);var l,h;const c=this._getRingWeight(this.index),{circumference:d,rotation:u}=this._getRotationExtents(),{ratioX:f,ratioY:p,offsetX:g,offsetY:m}=function(t,e,i){let n=1,s=1,o=0,r=0;if(e<Ge){const a=t,l=a+e,h=Math.cos(a),c=Math.sin(a),d=Math.cos(l),u=Math.sin(l),f=(t,e,n)=>mi(t,a,l,!0)?1:Math.max(e,e*i,n,n*i),p=(t,e,n)=>mi(t,a,l,!0)?-1:Math.min(e,e*i,n,n*i),g=f(0,h,d),m=f(ti,c,u),b=p(Ke,h,d),x=p(Ke+ti,c,u);n=(g-b)/2,s=(m-x)/2,o=-(g+b)/2,r=-(m+x)/2}return{ratioX:n,ratioY:s,offsetX:o,offsetY:r}}(u,d,a),b=(i.width-o)/f,x=(i.height-o)/p,y=Math.max(Math.min(b,x)/2,0),v=Le(this.options.radius,y),_=(v-Math.max(v*a,0))/this._getVisibleDatasetWeightTotal();this.offsetX=g*v,this.offsetY=m*v,n.total=this.calculateTotal(),this.outerRadius=v-_*this._getRingWeightOffset(this.index),this.innerRadius=Math.max(this.outerRadius-_*c,0),this.updateElements(s,0,s.length,t)}_circumference(t,e){const i=this.options,n=this._cachedMeta,s=this._getCircumference();return e&&i.animation.animateRotate||!this.chart.getDataVisibility(t)||null===n._parsed[t]||n.data[t].hidden?0:this.calculateCircumference(n._parsed[t]*s/Ge)}updateElements(t,e,i,n){const s="reset"===n,o=this.chart,r=o.chartArea,a=o.options.animation,l=(r.left+r.right)/2,h=(r.top+r.bottom)/2,c=s&&a.animateScale,d=c?0:this.innerRadius,u=c?0:this.outerRadius,{sharedOptions:f,includeOptions:p}=this._getSharedOptions(e,n);let g,m=this._getRotation();for(g=0;g<e;++g)m+=this._circumference(g,s);for(g=e;g<e+i;++g){const e=this._circumference(g,s),i=t[g],o={x:l+this.offsetX,y:h+this.offsetY,startAngle:m,endAngle:m+e,circumference:e,outerRadius:u,innerRadius:d};p&&(o.options=f||this.resolveDataElementOptions(g,i.active?"active":n)),m+=e,this.updateElement(i,g,o,n)}}calculateTotal(){const t=this._cachedMeta,e=t.data;let i,n=0;for(i=0;i<e.length;i++){const s=t._parsed[i];null===s||isNaN(s)||!this.chart.getDataVisibility(i)||e[i].hidden||(n+=Math.abs(s))}return n}calculateCircumference(t){const e=this._cachedMeta.total;return e>0&&!isNaN(t)?Ge*(Math.abs(t)/e):0}getLabelAndValue(t){const e=this._cachedMeta,i=this.chart,n=i.data.labels||[],s=ji(e._parsed[t],i.options.locale);return{label:n[t]||"",value:s}}getMaxBorderWidth(t){let e=0;const i=this.chart;let n,s,o,r,a;if(!t)for(n=0,s=i.data.datasets.length;n<s;++n)if(i.isDatasetVisible(n)){o=i.getDatasetMeta(n),t=o.data,r=o.controller;break}if(!t)return 0;for(n=0,s=t.length;n<s;++n)a=r.resolveDataElementOptions(n),"inner"!==a.borderAlign&&(e=Math.max(e,a.borderWidth||0,a.hoverBorderWidth||0));return e}getMaxOffset(t){let e=0;for(let i=0,n=t.length;i<n;++i){const t=this.resolveDataElementOptions(i);e=Math.max(e,t.offset||0,t.hoverOffset||0)}return e}_getRingWeightOffset(t){let e=0;for(let i=0;i<t;++i)this.chart.isDatasetVisible(i)&&(e+=this._getRingWeight(i));return e}_getRingWeight(t){return Math.max(Ee(this.chart.data.datasets[t].weight,1),0)}_getVisibleDatasetWeightTotal(){return this._getRingWeightOffset(this.chart.data.datasets.length)||1}}class js extends Ts{static id="polarArea";static defaults={dataElementType:"arc",animation:{animateRotate:!0,animateScale:!0},animations:{numbers:{type:"number",properties:["x","y","startAngle","endAngle","innerRadius","outerRadius"]}},indexAxis:"r",startAngle:0};static overrides={aspectRatio:1,plugins:{legend:{labels:{generateLabels(t){const e=t.data;if(e.labels.length&&e.datasets.length){const{labels:{pointStyle:i,color:n}}=t.legend.options;return e.labels.map((e,s)=>{const o=t.getDatasetMeta(0).controller.getStyle(s);return{text:e,fillStyle:o.backgroundColor,strokeStyle:o.borderColor,fontColor:n,lineWidth:o.borderWidth,pointStyle:i,hidden:!t.getDataVisibility(s),index:s}})}return[]}},onClick(t,e,i){i.chart.toggleDataVisibility(e.index),i.chart.update()}}},scales:{r:{type:"radialLinear",angleLines:{display:!1},beginAtZero:!0,grid:{circular:!0},pointLabels:{display:!1},startAngle:0}}};constructor(t,e){super(t,e),this.innerRadius=void 0,this.outerRadius=void 0}getLabelAndValue(t){const e=this._cachedMeta,i=this.chart,n=i.data.labels||[],s=ji(e._parsed[t].r,i.options.locale);return{label:n[t]||"",value:s}}parseObjectData(t,e,i,n){return Fn.bind(this)(t,e,i,n)}update(t){const e=this._cachedMeta.data;this._updateRadius(),this.updateElements(e,0,e.length,t)}getMinMax(){const t=this._cachedMeta,e={min:Number.POSITIVE_INFINITY,max:Number.NEGATIVE_INFINITY};return t.data.forEach((t,i)=>{const n=this.getParsed(i).r;!isNaN(n)&&this.chart.getDataVisibility(i)&&(n<e.min&&(e.min=n),n>e.max&&(e.max=n))}),e}_updateRadius(){const t=this.chart,e=t.chartArea,i=t.options,n=Math.min(e.right-e.left,e.bottom-e.top),s=Math.max(n/2,0),o=(s-Math.max(i.cutoutPercentage?s/100*i.cutoutPercentage:1,0))/t.getVisibleDatasetCount();this.outerRadius=s-o*this.index,this.innerRadius=this.outerRadius-o}updateElements(t,e,i,n){const s="reset"===n,o=this.chart,r=o.options.animation,a=this._cachedMeta.rScale,l=a.xCenter,h=a.yCenter,c=a.getIndexAngle(0)-.5*Ke;let d,u=c;const f=360/this.countVisibleElements();for(d=0;d<e;++d)u+=this._computeAngle(d,n,f);for(d=e;d<e+i;d++){const e=t[d];let i=u,p=u+this._computeAngle(d,n,f),g=o.getDataVisibility(d)?a.getDistanceFromCenterForValue(this.getParsed(d).r):0;u=p,s&&(r.animateScale&&(g=0),r.animateRotate&&(i=p=c));const m={x:l,y:h,innerRadius:0,outerRadius:g,startAngle:i,endAngle:p,options:this.resolveDataElementOptions(d,e.active?"active":n)};this.updateElement(e,d,m,n)}}countVisibleElements(){const t=this._cachedMeta;let e=0;return t.data.forEach((t,i)=>{!isNaN(this.getParsed(i).r)&&this.chart.getDataVisibility(i)&&e++}),e}_computeAngle(t,e,i){return this.chart.getDataVisibility(t)?hi(this.resolveDataElementOptions(t,e).angle||i):0}}var Ns=Object.freeze({__proto__:null,BarController:class extends Ts{static id="bar";static defaults={datasetElementType:!1,dataElementType:"bar",categoryPercentage:.8,barPercentage:.9,grouped:!0,animations:{numbers:{type:"number",properties:["x","y","base","width","height"]}}};static overrides={scales:{_index_:{type:"category",offset:!0,grid:{offset:!0}},_value_:{type:"linear",beginAtZero:!0}}};parsePrimitiveData(t,e,i,n){return Rs(t,e,i,n)}parseArrayData(t,e,i,n){return Rs(t,e,i,n)}parseObjectData(t,e,i,n){const{iScale:s,vScale:o}=t,{xAxisKey:r="x",yAxisKey:a="y"}=this._parsing,l="x"===s.axis?r:a,h="x"===o.axis?r:a,c=[];let d,u,f,p;for(d=i,u=i+n;d<u;++d)p=e[d],f={},f[s.axis]=s.parse($e(p,l),d),c.push(Ls($e(p,h),f,o,d));return c}updateRangeFromParsed(t,e,i,n){super.updateRangeFromParsed(t,e,i,n);const s=i._custom;s&&e===this._cachedMeta.vScale&&(t.min=Math.min(t.min,s.min),t.max=Math.max(t.max,s.max))}getMaxOverflow(){return 0}getLabelAndValue(t){const e=this._cachedMeta,{iScale:i,vScale:n}=e,s=this.getParsed(t),o=s._custom,r=Is(o)?"["+o.start+", "+o.end+"]":""+n.getLabelForValue(s[n.axis]);return{label:""+i.getLabelForValue(s[i.axis]),value:r}}initialize(){this.enableOptionSharing=!0,super.initialize(),this._cachedMeta.stack=this.getDataset().stack}update(t){const e=this._cachedMeta;this.updateElements(e.data,0,e.data.length,t)}updateElements(t,e,i,n){const s="reset"===n,{index:o,_cachedMeta:{vScale:r}}=this,a=r.getBasePixel(),l=r.isHorizontal(),h=this._getRuler(),{sharedOptions:c,includeOptions:d}=this._getSharedOptions(e,n);for(let u=e;u<e+i;u++){const e=this.getParsed(u),i=s||De(e[r.axis])?{base:a,head:a}:this._calculateBarValuePixels(u),f=this._calculateBarIndexPixels(u,h),p=(e._stacks||{})[r.axis],g={horizontal:l,base:i.base,enableBorderRadius:!p||Is(e._custom)||o===p._top||o===p._bottom,x:l?i.head:f.center,y:l?f.center:i.head,height:l?f.size:Math.abs(i.size),width:l?Math.abs(i.size):f.size};d&&(g.options=c||this.resolveDataElementOptions(u,t[u].active?"active":n));const m=g.options||t[u].options;Fs(g,m,p,o),Bs(g,m,h.ratio),this.updateElement(t[u],u,g,n)}}_getStacks(t,e){const{iScale:i}=this._cachedMeta,n=i.getMatchingVisibleMetas(this._type).filter(t=>t.controller.options.grouped),s=i.options.stacked,o=[],r=this._cachedMeta.controller.getParsed(e),a=r&&r[i.axis],l=t=>{const e=t._parsed.find(t=>t[i.axis]===a),n=e&&e[t.vScale.axis];if(De(n)||isNaN(n))return!0};for(const i of n)if((void 0===e||!l(i))&&((!1===s||-1===o.indexOf(i.stack)||void 0===s&&void 0===i.stack)&&o.push(i.stack),i.index===t))break;return o.length||o.push(void 0),o}_getStackCount(t){return this._getStacks(void 0,t).length}_getAxisCount(){return this._getAxis().length}getFirstScaleIdForIndexAxis(){const t=this.chart.scales,e=this.chart.options.indexAxis;return Object.keys(t).filter(i=>t[i].axis===e).shift()}_getAxis(){const t={},e=this.getFirstScaleIdForIndexAxis();for(const i of this.chart.data.datasets)t[Ee("x"===this.chart.options.indexAxis?i.xAxisID:i.yAxisID,e)]=!0;return Object.keys(t)}_getStackIndex(t,e,i){const n=this._getStacks(t,i),s=void 0!==e?n.indexOf(e):-1;return-1===s?n.length-1:s}_getRuler(){const t=this.options,e=this._cachedMeta,i=e.iScale,n=[];let s,o;for(s=0,o=e.data.length;s<o;++s)n.push(i.getPixelForValue(this.getParsed(s)[i.axis],s));const r=t.barThickness;return{min:r||Es(e),pixels:n,start:i._startPixel,end:i._endPixel,stackCount:this._getStackCount(),scale:i,grouped:t.grouped,ratio:r?1:t.categoryPercentage*t.barPercentage}}_calculateBarValuePixels(t){const{_cachedMeta:{vScale:e,_stacked:i,index:n},options:{base:s,minBarLength:o}}=this,r=s||0,a=this.getParsed(t),l=a._custom,h=Is(l);let c,d,u=a[e.axis],f=0,p=i?this.applyStack(e,a,i):u;p!==u&&(f=p-u,p=u),h&&(u=l.barStart,p=l.barEnd-l.barStart,0!==u&&si(u)!==si(l.barEnd)&&(f=0),f+=u);const g=De(s)||h?f:s;let m=e.getPixelForValue(g);if(c=this.chart.getDataVisibility(t)?e.getPixelForValue(f+p):m,d=c-m,Math.abs(d)<o){d=function(t,e,i){return 0!==t?si(t):(e.isHorizontal()?1:-1)*(e.min>=i?1:-1)}(d,e,r)*o,u===r&&(m-=d/2);const t=e.getPixelForDecimal(0),s=e.getPixelForDecimal(1),l=Math.min(t,s),f=Math.max(t,s);m=Math.max(Math.min(m,f),l),c=m+d,i&&!h&&(a._stacks[e.axis]._visualValues[n]=e.getValueForPixel(c)-e.getValueForPixel(m))}if(m===e.getPixelForValue(r)){const t=si(d)*e.getLineWidthForValue(r)/2;m+=t,d-=t}return{size:d,base:m,head:c,center:c+d/2}}_calculateBarIndexPixels(t,e){const i=e.scale,n=this.options,s=n.skipNull,o=Ee(n.maxBarThickness,1/0);let r,a;const l=this._getAxisCount();if(e.grouped){const i=s?this._getStackCount(t):e.stackCount,h="flex"===n.barThickness?function(t,e,i,n){const s=e.pixels,o=s[t];let r=t>0?s[t-1]:null,a=t<s.length-1?s[t+1]:null;const l=i.categoryPercentage;null===r&&(r=o-(null===a?e.end-e.start:a-o)),null===a&&(a=o+o-r);const h=o-(o-Math.min(r,a))/2*l;return{chunk:Math.abs(a-r)/2*l/n,ratio:i.barPercentage,start:h}}(t,e,n,i*l):function(t,e,i,n){const s=i.barThickness;let o,r;return De(s)?(o=e.min*i.categoryPercentage,r=i.barPercentage):(o=s*n,r=1),{chunk:o/n,ratio:r,start:e.pixels[t]-o/2}}(t,e,n,i*l),c="x"===this.chart.options.indexAxis?this.getDataset().xAxisID:this.getDataset().yAxisID,d=this._getAxis().indexOf(Ee(c,this.getFirstScaleIdForIndexAxis())),u=this._getStackIndex(this.index,this._cachedMeta.stack,s?t:void 0)+d;r=h.start+h.chunk*u+h.chunk/2,a=Math.min(o,h.chunk*h.ratio)}else r=i.getPixelForValue(this.getParsed(t)[i.axis],t),a=Math.min(o,e.min*e.ratio);return{base:r-a/2,head:r+a/2,center:r,size:a}}draw(){const t=this._cachedMeta,e=t.vScale,i=t.data,n=i.length;let s=0;for(;s<n;++s)null===this.getParsed(s)[e.axis]||i[s].hidden||i[s].draw(this._ctx)}},BubbleController:class extends Ts{static id="bubble";static defaults={datasetElementType:!1,dataElementType:"point",animations:{numbers:{type:"number",properties:["x","y","borderWidth","radius"]}}};static overrides={scales:{x:{type:"linear"},y:{type:"linear"}}};initialize(){this.enableOptionSharing=!0,super.initialize()}parsePrimitiveData(t,e,i,n){const s=super.parsePrimitiveData(t,e,i,n);for(let t=0;t<s.length;t++)s[t]._custom=this.resolveDataElementOptions(t+i).radius;return s}parseArrayData(t,e,i,n){const s=super.parseArrayData(t,e,i,n);for(let t=0;t<s.length;t++){const n=e[i+t];s[t]._custom=Ee(n[2],this.resolveDataElementOptions(t+i).radius)}return s}parseObjectData(t,e,i,n){const s=super.parseObjectData(t,e,i,n);for(let t=0;t<s.length;t++){const n=e[i+t];s[t]._custom=Ee(n&&n.r&&+n.r,this.resolveDataElementOptions(t+i).radius)}return s}getMaxOverflow(){const t=this._cachedMeta.data;let e=0;for(let i=t.length-1;i>=0;--i)e=Math.max(e,t[i].size(this.resolveDataElementOptions(i))/2);return e>0&&e}getLabelAndValue(t){const e=this._cachedMeta,i=this.chart.data.labels||[],{xScale:n,yScale:s}=e,o=this.getParsed(t),r=n.getLabelForValue(o.x),a=s.getLabelForValue(o.y),l=o._custom;return{label:i[t]||"",value:"("+r+", "+a+(l?", "+l:"")+")"}}update(t){const e=this._cachedMeta.data;this.updateElements(e,0,e.length,t)}updateElements(t,e,i,n){const s="reset"===n,{iScale:o,vScale:r}=this._cachedMeta,{sharedOptions:a,includeOptions:l}=this._getSharedOptions(e,n),h=o.axis,c=r.axis;for(let d=e;d<e+i;d++){const e=t[d],i=!s&&this.getParsed(d),u={},f=u[h]=s?o.getPixelForDecimal(.5):o.getPixelForValue(i[h]),p=u[c]=s?r.getBasePixel():r.getPixelForValue(i[c]);u.skip=isNaN(f)||isNaN(p),l&&(u.options=a||this.resolveDataElementOptions(d,e.active?"active":n),s&&(u.options.radius=0)),this.updateElement(e,d,u,n)}}resolveDataElementOptions(t,e){const i=this.getParsed(t);let n=super.resolveDataElementOptions(t,e);n.$shared&&(n=Object.assign({},n,{$shared:!1}));const s=n.radius;return"active"!==e&&(n.radius=0),n.radius+=Ee(i&&i._custom,s),n}},DoughnutController:Ws,LineController:class extends Ts{static id="line";static defaults={datasetElementType:"line",dataElementType:"point",showLine:!0,spanGaps:!1};static overrides={scales:{_index_:{type:"category"},_value_:{type:"linear"}}};initialize(){this.enableOptionSharing=!0,this.supportsDecimation=!0,super.initialize()}update(t){const e=this._cachedMeta,{dataset:i,data:n=[],_dataset:s}=e,o=this.chart._animationsDisabled;let{start:r,count:a}=Ai(e,n,o);this._drawStart=r,this._drawCount=a,Ci(e)&&(r=0,a=n.length),i._chart=this.chart,i._datasetIndex=this.index,i._decimated=!!s._decimated,i.points=n;const l=this.resolveDatasetElementOptions(t);this.options.showLine||(l.borderWidth=0),l.segment=this.options.segment,this.updateElement(i,void 0,{animated:!o,options:l},t),this.updateElements(n,r,a,t)}updateElements(t,e,i,n){const s="reset"===n,{iScale:o,vScale:r,_stacked:a,_dataset:l}=this._cachedMeta,{sharedOptions:h,includeOptions:c}=this._getSharedOptions(e,n),d=o.axis,u=r.axis,{spanGaps:f,segment:p}=this.options,g=ai(f)?f:Number.POSITIVE_INFINITY,m=this.chart._animationsDisabled||s||"none"===n,b=e+i,x=t.length;let y=e>0&&this.getParsed(e-1);for(let i=0;i<x;++i){const f=t[i],x=m?f:{};if(i<e||i>=b){x.skip=!0;continue}const v=this.getParsed(i),_=De(v[u]),w=x[d]=o.getPixelForValue(v[d],i),M=x[u]=s||_?r.getBasePixel():r.getPixelForValue(a?this.applyStack(r,v,a):v[u],i);x.skip=isNaN(w)||isNaN(M)||_,x.stop=i>0&&Math.abs(v[d]-y[d])>g,p&&(x.parsed=v,x.raw=l.data[i]),c&&(x.options=h||this.resolveDataElementOptions(i,f.active?"active":n)),m||this.updateElement(f,i,x,n),y=v}}getMaxOverflow(){const t=this._cachedMeta,e=t.dataset,i=e.options&&e.options.borderWidth||0,n=t.data||[];if(!n.length)return i;const s=n[0].size(this.resolveDataElementOptions(0)),o=n[n.length-1].size(this.resolveDataElementOptions(n.length-1));return Math.max(i,s,o)/2}draw(){const t=this._cachedMeta;t.dataset.updateControlPoints(this.chart.chartArea,t.iScale.axis),super.draw()}},PieController:class extends Ws{static id="pie";static defaults={cutout:0,rotation:0,circumference:360,radius:"100%"}},PolarAreaController:js,RadarController:class extends Ts{static id="radar";static defaults={datasetElementType:"line",dataElementType:"point",indexAxis:"r",showLine:!0,elements:{line:{fill:"start"}}};static overrides={aspectRatio:1,scales:{r:{type:"radialLinear"}}};getLabelAndValue(t){const e=this._cachedMeta.vScale,i=this.getParsed(t);return{label:e.getLabels()[t],value:""+e.getLabelForValue(i[e.axis])}}parseObjectData(t,e,i,n){return Fn.bind(this)(t,e,i,n)}update(t){const e=this._cachedMeta,i=e.dataset,n=e.data||[],s=e.iScale.getLabels();if(i.points=n,"resize"!==t){const e=this.resolveDatasetElementOptions(t);this.options.showLine||(e.borderWidth=0);const o={_loop:!0,_fullLoop:s.length===n.length,options:e};this.updateElement(i,void 0,o,t)}this.updateElements(n,0,n.length,t)}updateElements(t,e,i,n){const s=this._cachedMeta.rScale,o="reset"===n;for(let r=e;r<e+i;r++){const e=t[r],i=this.resolveDataElementOptions(r,e.active?"active":n),a=s.getPointPositionForValue(r,this.getParsed(r).r),l=o?s.xCenter:a.x,h=o?s.yCenter:a.y,c={x:l,y:h,angle:a.angle,skip:isNaN(l)||isNaN(h),options:i};this.updateElement(e,r,c,n)}}},ScatterController:class extends Ts{static id="scatter";static defaults={datasetElementType:!1,dataElementType:"point",showLine:!1,fill:!1};static overrides={interaction:{mode:"point"},scales:{x:{type:"linear"},y:{type:"linear"}}};getLabelAndValue(t){const e=this._cachedMeta,i=this.chart.data.labels||[],{xScale:n,yScale:s}=e,o=this.getParsed(t),r=n.getLabelForValue(o.x),a=s.getLabelForValue(o.y);return{label:i[t]||"",value:"("+r+", "+a+")"}}update(t){const e=this._cachedMeta,{data:i=[]}=e,n=this.chart._animationsDisabled;let{start:s,count:o}=Ai(e,i,n);if(this._drawStart=s,this._drawCount=o,Ci(e)&&(s=0,o=i.length),this.options.showLine){this.datasetElementType||this.addElements();const{dataset:s,_dataset:o}=e;s._chart=this.chart,s._datasetIndex=this.index,s._decimated=!!o._decimated,s.points=i;const r=this.resolveDatasetElementOptions(t);r.segment=this.options.segment,this.updateElement(s,void 0,{animated:!n,options:r},t)}else this.datasetElementType&&(delete e.dataset,this.datasetElementType=!1);this.updateElements(i,s,o,t)}addElements(){const{showLine:t}=this.options;!this.datasetElementType&&t&&(this.datasetElementType=this.chart.registry.getElement("line")),super.addElements()}updateElements(t,e,i,n){const s="reset"===n,{iScale:o,vScale:r,_stacked:a,_dataset:l}=this._cachedMeta,h=this.resolveDataElementOptions(e,n),c=this.getSharedOptions(h),d=this.includeOptions(n,c),u=o.axis,f=r.axis,{spanGaps:p,segment:g}=this.options,m=ai(p)?p:Number.POSITIVE_INFINITY,b=this.chart._animationsDisabled||s||"none"===n;let x=e>0&&this.getParsed(e-1);for(let h=e;h<e+i;++h){const e=t[h],i=this.getParsed(h),p=b?e:{},y=De(i[f]),v=p[u]=o.getPixelForValue(i[u],h),_=p[f]=s||y?r.getBasePixel():r.getPixelForValue(a?this.applyStack(r,i,a):i[f],h);p.skip=isNaN(v)||isNaN(_)||y,p.stop=h>0&&Math.abs(i[u]-x[u])>m,g&&(p.parsed=i,p.raw=l.data[h]),d&&(p.options=c||this.resolveDataElementOptions(h,e.active?"active":n)),b||this.updateElement(e,h,p,n),x=i}this.updateSharedOptions(c,n,h)}getMaxOverflow(){const t=this._cachedMeta,e=t.data||[];if(!this.options.showLine){let t=0;for(let i=e.length-1;i>=0;--i)t=Math.max(t,e[i].size(this.resolveDataElementOptions(i))/2);return t>0&&t}const i=t.dataset,n=i.options&&i.options.borderWidth||0;if(!e.length)return n;const s=e[0].size(this.resolveDataElementOptions(0)),o=e[e.length-1].size(this.resolveDataElementOptions(e.length-1));return Math.max(n,s,o)/2}}});function Hs(){throw new Error("This method is not implemented: Check that a complete date adapter is provided.")}class $s{static override(t){Object.assign($s.prototype,t)}options;constructor(t){this.options=t||{}}init(){}formats(){return Hs()}parse(){return Hs()}format(){return Hs()}add(){return Hs()}diff(){return Hs()}startOf(){return Hs()}endOf(){return Hs()}}var Us=$s;function Ys(t,e,i,n){const{controller:s,data:o,_sorted:r}=t,a=s._cachedMeta.iScale,l=t.dataset&&t.dataset.options?t.dataset.options.spanGaps:null;if(a&&e===a.axis&&"r"!==e&&r&&o.length){const r=a._reversePixels?_i:vi;if(!n){const n=r(o,e,i);if(l){const{vScale:e}=s._cachedMeta,{_parsed:i}=t,o=i.slice(0,n.lo+1).reverse().findIndex(t=>!De(t[e.axis]));n.lo-=Math.max(0,o);const r=i.slice(n.hi).findIndex(t=>!De(t[e.axis]));n.hi+=Math.max(0,r)}return n}if(s._sharedOptions){const t=o[0],n="function"==typeof t.getRange&&t.getRange(e);if(n){const t=r(o,e,i-n),s=r(o,e,i+n);return{lo:t.lo,hi:s.hi}}}}return{lo:0,hi:o.length-1}}function Xs(t,e,i,n,s){const o=t.getSortedVisibleDatasetMetas(),r=i[e];for(let t=0,i=o.length;t<i;++t){const{index:i,data:a}=o[t],{lo:l,hi:h}=Ys(o[t],e,r,s);for(let t=l;t<=h;++t){const e=a[t];e.skip||n(e,i,t)}}}function qs(t,e,i,n,s){const o=[];return s||t.isPointInArea(e)?(Xs(t,i,e,function(i,r,a){(s||nn(i,t.chartArea,0))&&i.inRange(e.x,e.y,n)&&o.push({element:i,datasetIndex:r,index:a})},!0),o):o}function Ks(t,e,i,n,s,o){return o||t.isPointInArea(e)?"r"!==i||n?function(t,e,i,n,s,o){let r=[];const a=function(t){const e=-1!==t.indexOf("x"),i=-1!==t.indexOf("y");return function(t,n){const s=e?Math.abs(t.x-n.x):0,o=i?Math.abs(t.y-n.y):0;return Math.sqrt(Math.pow(s,2)+Math.pow(o,2))}}(i);let l=Number.POSITIVE_INFINITY;return Xs(t,i,e,function(i,h,c){const d=i.inRange(e.x,e.y,s);if(n&&!d)return;const u=i.getCenterPoint(s);if(!o&&!t.isPointInArea(u)&&!d)return;const f=a(e,u);f<l?(r=[{element:i,datasetIndex:h,index:c}],l=f):f===l&&r.push({element:i,datasetIndex:h,index:c})}),r}(t,e,i,n,s,o):function(t,e,i,n){let s=[];return Xs(t,i,e,function(t,i,o){const{startAngle:r,endAngle:a}=t.getProps(["startAngle","endAngle"],n),{angle:l}=ui(t,{x:e.x,y:e.y});mi(l,r,a)&&s.push({element:t,datasetIndex:i,index:o})}),s}(t,e,i,s):[]}function Gs(t,e,i,n,s){const o=[],r="x"===i?"inXRange":"inYRange";let a=!1;return Xs(t,i,e,(t,n,l)=>{t[r]&&t[r](e[i],s)&&(o.push({element:t,datasetIndex:n,index:l}),a=a||t.inRange(e.x,e.y,s))}),n&&!a?[]:o}var Js={evaluateInteractionItems:Xs,modes:{index(t,e,i,n){const s=Kn(e,t),o=i.axis||"x",r=i.includeInvisible||!1,a=i.intersect?qs(t,s,o,n,r):Ks(t,s,o,!1,n,r),l=[];return a.length?(t.getSortedVisibleDatasetMetas().forEach(t=>{const e=a[0].index,i=t.data[e];i&&!i.skip&&l.push({element:i,datasetIndex:t.index,index:e})}),l):[]},dataset(t,e,i,n){const s=Kn(e,t),o=i.axis||"xy",r=i.includeInvisible||!1;let a=i.intersect?qs(t,s,o,n,r):Ks(t,s,o,!1,n,r);if(a.length>0){const e=a[0].datasetIndex,i=t.getDatasetMeta(e).data;a=[];for(let t=0;t<i.length;++t)a.push({element:i[t],datasetIndex:e,index:t})}return a},point(t,e,i,n){return qs(t,Kn(e,t),i.axis||"xy",n,i.includeInvisible||!1)},nearest(t,e,i,n){const s=Kn(e,t),o=i.axis||"xy",r=i.includeInvisible||!1;return Ks(t,s,o,i.intersect,n,r)},x(t,e,i,n){return Gs(t,Kn(e,t),"x",i.intersect,n)},y(t,e,i,n){return Gs(t,Kn(e,t),"y",i.intersect,n)}}};const Zs=["left","top","right","bottom"];function Qs(t,e){return t.filter(t=>t.pos===e)}function to(t,e){return t.filter(t=>-1===Zs.indexOf(t.pos)&&t.box.axis===e)}function eo(t,e){return t.sort((t,i)=>{const n=e?i:t,s=e?t:i;return n.weight===s.weight?n.index-s.index:n.weight-s.weight})}function io(t,e,i,n){return Math.max(t[i],e[i])+Math.max(t[n],e[n])}function no(t,e){t.top=Math.max(t.top,e.top),t.left=Math.max(t.left,e.left),t.bottom=Math.max(t.bottom,e.bottom),t.right=Math.max(t.right,e.right)}function so(t,e,i,n){const{pos:s,box:o}=i,r=t.maxPadding;if(!Ae(s)){i.size&&(t[s]-=i.size);const e=n[i.stack]||{size:0,count:1};e.size=Math.max(e.size,i.horizontal?o.height:o.width),i.size=e.size/e.count,t[s]+=i.size}o.getPadding&&no(r,o.getPadding());const a=Math.max(0,e.outerWidth-io(r,t,"left","right")),l=Math.max(0,e.outerHeight-io(r,t,"top","bottom")),h=a!==t.w,c=l!==t.h;return t.w=a,t.h=l,i.horizontal?{same:h,other:c}:{same:c,other:h}}function oo(t,e){const i=e.maxPadding;return function(t){const n={left:0,top:0,right:0,bottom:0};return t.forEach(t=>{n[t]=Math.max(e[t],i[t])}),n}(t?["left","right"]:["top","bottom"])}function ro(t,e,i,n){const s=[];let o,r,a,l,h,c;for(o=0,r=t.length,h=0;o<r;++o){a=t[o],l=a.box,l.update(a.width||e.w,a.height||e.h,oo(a.horizontal,e));const{same:r,other:d}=so(e,i,a,n);h|=r&&s.length,c=c||d,l.fullSize||s.push(a)}return h&&ro(s,e,i,n)||c}function ao(t,e,i,n,s){t.top=i,t.left=e,t.right=e+n,t.bottom=i+s,t.width=n,t.height=s}function lo(t,e,i,n){const s=i.padding;let{x:o,y:r}=e;for(const a of t){const t=a.box,l=n[a.stack]||{count:1,placed:0,weight:1},h=a.stackWeight/l.weight||1;if(a.horizontal){const n=e.w*h,o=l.size||t.height;Ye(l.start)&&(r=l.start),t.fullSize?ao(t,s.left,r,i.outerWidth-s.right-s.left,o):ao(t,e.left+l.placed,r,n,o),l.start=r,l.placed+=n,r=t.bottom}else{const n=e.h*h,r=l.size||t.width;Ye(l.start)&&(o=l.start),t.fullSize?ao(t,o,s.top,r,i.outerHeight-s.bottom-s.top):ao(t,o,e.top+l.placed,r,n),l.start=o,l.placed+=n,o=t.right}}e.x=o,e.y=r}var ho={addBox(t,e){t.boxes||(t.boxes=[]),e.fullSize=e.fullSize||!1,e.position=e.position||"top",e.weight=e.weight||0,e._layers=e._layers||function(){return[{z:0,draw(t){e.draw(t)}}]},t.boxes.push(e)},removeBox(t,e){const i=t.boxes?t.boxes.indexOf(e):-1;-1!==i&&t.boxes.splice(i,1)},configure(t,e,i){e.fullSize=i.fullSize,e.position=i.position,e.weight=i.weight},update(t,e,i,n){if(!t)return;const s=yn(t.options.layout.padding),o=Math.max(e-s.width,0),r=Math.max(i-s.height,0),a=function(t){const e=function(t){const e=[];let i,n,s,o,r,a;for(i=0,n=(t||[]).length;i<n;++i)s=t[i],({position:o,options:{stack:r,stackWeight:a=1}}=s),e.push({index:i,box:s,pos:o,horizontal:s.isHorizontal(),weight:s.weight,stack:r&&o+r,stackWeight:a});return e}(t),i=eo(e.filter(t=>t.box.fullSize),!0),n=eo(Qs(e,"left"),!0),s=eo(Qs(e,"right")),o=eo(Qs(e,"top"),!0),r=eo(Qs(e,"bottom")),a=to(e,"x"),l=to(e,"y");return{fullSize:i,leftAndTop:n.concat(o),rightAndBottom:s.concat(l).concat(r).concat(a),chartArea:Qs(e,"chartArea"),vertical:n.concat(s).concat(l),horizontal:o.concat(r).concat(a)}}(t.boxes),l=a.vertical,h=a.horizontal;Ie(t.boxes,t=>{"function"==typeof t.beforeLayout&&t.beforeLayout()});const c=l.reduce((t,e)=>e.box.options&&!1===e.box.options.display?t:t+1,0)||1,d=Object.freeze({outerWidth:e,outerHeight:i,padding:s,availableWidth:o,availableHeight:r,vBoxMaxWidth:o/2/c,hBoxMaxHeight:r/2}),u=Object.assign({},s);no(u,yn(n));const f=Object.assign({maxPadding:u,w:o,h:r,x:s.left,y:s.top},s),p=function(t,e){const i=function(t){const e={};for(const i of t){const{stack:t,pos:n,stackWeight:s}=i;if(!t||!Zs.includes(n))continue;const o=e[t]||(e[t]={count:0,placed:0,weight:0,size:0});o.count++,o.weight+=s}return e}(t),{vBoxMaxWidth:n,hBoxMaxHeight:s}=e;let o,r,a;for(o=0,r=t.length;o<r;++o){a=t[o];const{fullSize:r}=a.box,l=i[a.stack],h=l&&a.stackWeight/l.weight;a.horizontal?(a.width=h?h*n:r&&e.availableWidth,a.height=s):(a.width=n,a.height=h?h*s:r&&e.availableHeight)}return i}(l.concat(h),d);ro(a.fullSize,f,d,p),ro(l,f,d,p),ro(h,f,d,p)&&ro(l,f,d,p),function(t){const e=t.maxPadding;function i(i){const n=Math.max(e[i]-t[i],0);return t[i]+=n,n}t.y+=i("top"),t.x+=i("left"),i("right"),i("bottom")}(f),lo(a.leftAndTop,f,d,p),f.x+=f.w,f.y+=f.h,lo(a.rightAndBottom,f,d,p),t.chartArea={left:f.left,top:f.top,right:f.left+f.w,bottom:f.top+f.h,height:f.h,width:f.w},Ie(a.chartArea,e=>{const i=e.box;Object.assign(i,t.chartArea),i.update(f.w,f.h,{left:0,top:0,right:0,bottom:0})})}};class co{acquireContext(t,e){}releaseContext(t){return!1}addEventListener(t,e,i){}removeEventListener(t,e,i){}getDevicePixelRatio(){return 1}getMaximumSize(t,e,i,n){return e=Math.max(0,e||t.width),i=i||t.height,{width:e,height:Math.max(0,n?Math.floor(e/n):i)}}isAttached(t){return!0}updateConfig(t){}}class uo extends co{acquireContext(t){return t&&t.getContext&&t.getContext("2d")||null}updateConfig(t){t.options.animation=!1}}const fo="$chartjs",po={touchstart:"mousedown",touchmove:"mousemove",touchend:"mouseup",pointerenter:"mouseenter",pointerdown:"mousedown",pointermove:"mousemove",pointerup:"mouseup",pointerleave:"mouseout",pointerout:"mouseout"},go=t=>null===t||""===t,mo=!!Zn&&{passive:!0};function bo(t,e,i){t&&t.canvas&&t.canvas.removeEventListener(e,i,mo)}function xo(t,e){for(const i of t)if(i===e||i.contains(e))return!0}function yo(t,e,i){const n=t.canvas,s=new MutationObserver(t=>{let e=!1;for(const i of t)e=e||xo(i.addedNodes,n),e=e&&!xo(i.removedNodes,n);e&&i()});return s.observe(document,{childList:!0,subtree:!0}),s}function vo(t,e,i){const n=t.canvas,s=new MutationObserver(t=>{let e=!1;for(const i of t)e=e||xo(i.removedNodes,n),e=e&&!xo(i.addedNodes,n);e&&i()});return s.observe(document,{childList:!0,subtree:!0}),s}const _o=new Map;let wo=0;function Mo(){const t=window.devicePixelRatio;t!==wo&&(wo=t,_o.forEach((e,i)=>{i.currentDevicePixelRatio!==t&&e()}))}function ko(t,e,i){const n=t.canvas,s=n&&$n(n);if(!s)return;const o=Oi((t,e)=>{const n=s.clientWidth;i(t,e),n<s.clientWidth&&i()},window),r=new ResizeObserver(t=>{const e=t[0],i=e.contentRect.width,n=e.contentRect.height;0===i&&0===n||o(i,n)});return r.observe(s),function(t,e){_o.size||window.addEventListener("resize",Mo),_o.set(t,e)}(t,o),r}function So(t,e,i){i&&i.disconnect(),"resize"===e&&function(t){_o.delete(t),_o.size||window.removeEventListener("resize",Mo)}(t)}function Oo(t,e,i){const n=t.canvas,s=Oi(e=>{null!==t.ctx&&i(function(t,e){const i=po[t.type]||t.type,{x:n,y:s}=Kn(t,e);return{type:i,chart:e,native:t,x:void 0!==n?n:null,y:void 0!==s?s:null}}(e,t))},t);return function(t,e,i){t&&t.addEventListener(e,i,mo)}(n,e,s),s}class Do extends co{acquireContext(t,e){const i=t&&t.getContext&&t.getContext("2d");return i&&i.canvas===t?(function(t,e){const i=t.style,n=t.getAttribute("height"),s=t.getAttribute("width");if(t[fo]={initial:{height:n,width:s,style:{display:i.display,height:i.height,width:i.width}}},i.display=i.display||"block",i.boxSizing=i.boxSizing||"border-box",go(s)){const e=Qn(t,"width");void 0!==e&&(t.width=e)}if(go(n))if(""===t.style.height)t.height=t.width/(e||2);else{const e=Qn(t,"height");void 0!==e&&(t.height=e)}}(t,e),i):null}releaseContext(t){const e=t.canvas;if(!e[fo])return!1;const i=e[fo].initial;["height","width"].forEach(t=>{const n=i[t];De(n)?e.removeAttribute(t):e.setAttribute(t,n)});const n=i.style||{};return Object.keys(n).forEach(t=>{e.style[t]=n[t]}),e.width=e.width,delete e[fo],!0}addEventListener(t,e,i){this.removeEventListener(t,e);const n=t.$proxies||(t.$proxies={}),s={attach:yo,detach:vo,resize:ko}[e]||Oo;n[e]=s(t,e,i)}removeEventListener(t,e){const i=t.$proxies||(t.$proxies={}),n=i[e];n&&(({attach:So,detach:So,resize:So}[e]||bo)(t,e,n),i[e]=void 0)}getDevicePixelRatio(){return window.devicePixelRatio}getMaximumSize(t,e,i,n){return function(t,e,i,n){const s=Yn(t),o=qn(s,"margin"),r=Un(s.maxWidth,t,"clientWidth")||Ze,a=Un(s.maxHeight,t,"clientHeight")||Ze,l=function(t,e,i){let n,s;if(void 0===e||void 0===i){const o=t&&$n(t);if(o){const t=o.getBoundingClientRect(),r=Yn(o),a=qn(r,"border","width"),l=qn(r,"padding");e=t.width-l.width-a.width,i=t.height-l.height-a.height,n=Un(r.maxWidth,o,"clientWidth"),s=Un(r.maxHeight,o,"clientHeight")}else e=t.clientWidth,i=t.clientHeight}return{width:e,height:i,maxWidth:n||Ze,maxHeight:s||Ze}}(t,e,i);let{width:h,height:c}=l;if("content-box"===s.boxSizing){const t=qn(s,"border","width"),e=qn(s,"padding");h-=e.width+t.width,c-=e.height+t.height}return h=Math.max(0,h-o.width),c=Math.max(0,n?h/n:c-o.height),h=Gn(Math.min(h,r,l.maxWidth)),c=Gn(Math.min(c,a,l.maxHeight)),h&&!c&&(c=Gn(h/2)),(void 0!==e||void 0!==i)&&n&&l.height&&c>l.height&&(c=l.height,h=Gn(Math.floor(c*n))),{width:h,height:c}}(t,e,i,n)}isAttached(t){const e=t&&$n(t);return!(!e||!e.isConnected)}}class Po{static defaults={};static defaultRoutes=void 0;x;y;active=!1;options;$animations;tooltipPosition(t){const{x:e,y:i}=this.getProps(["x","y"],t);return{x:e,y:i}}hasValue(){return ai(this.x)&&ai(this.y)}getProps(t,e){const i=this.$animations;if(!e||!i)return this;const n={};return t.forEach(t=>{n[t]=i[t]&&i[t].active()?i[t]._to:this[t]}),n}}function Ao(t,e,i,n,s){const o=Ee(n,0),r=Math.min(Ee(s,t.length),t.length);let a,l,h,c=0;for(i=Math.ceil(i),s&&(a=s-n,i=a/Math.floor(a/i)),h=o;h<0;)c++,h=Math.round(o+c*i);for(l=Math.max(o,0);l<r;l++)l===h&&(e.push(t[l]),c++,h=Math.round(o+c*i))}const Co=(t,e,i)=>"top"===e||"left"===e?t[e]+i:t[e]-i,To=(t,e)=>Math.min(e||t,t);function Eo(t,e){const i=[],n=t.length/e,s=t.length;let o=0;for(;o<s;o+=n)i.push(t[Math.floor(o)]);return i}function Lo(t,e,i){const n=t.ticks.length,s=Math.min(e,n-1),o=t._startPixel,r=t._endPixel,a=1e-6;let l,h=t.getPixelForTick(s);if(!(i&&(l=1===n?Math.max(h-o,r-h):0===e?(t.getPixelForTick(1)-h)/2:(h-t.getPixelForTick(s-1))/2,h+=s<e?l:-l,h<o-a||h>r+a)))return h}function Ro(t){return t.drawTicks?t.tickLength:0}function Io(t,e){if(!t.display)return 0;const i=vn(t.font,e),n=yn(t.padding);return(Pe(t.text)?t.text.length:1)*i.lineHeight+n.height}function Fo(t,e,i){let n=Di(t);return(i&&"right"!==e||!i&&"right"===e)&&(n=(t=>"left"===t?"right":"right"===t?"left":t)(n)),n}class zo extends Po{constructor(t){super(),this.id=t.id,this.type=t.type,this.options=void 0,this.ctx=t.ctx,this.chart=t.chart,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.width=void 0,this.height=void 0,this._margins={left:0,right:0,top:0,bottom:0},this.maxWidth=void 0,this.maxHeight=void 0,this.paddingTop=void 0,this.paddingBottom=void 0,this.paddingLeft=void 0,this.paddingRight=void 0,this.axis=void 0,this.labelRotation=void 0,this.min=void 0,this.max=void 0,this._range=void 0,this.ticks=[],this._gridLineItems=null,this._labelItems=null,this._labelSizes=null,this._length=0,this._maxLength=0,this._longestTextCache={},this._startPixel=void 0,this._endPixel=void 0,this._reversePixels=!1,this._userMax=void 0,this._userMin=void 0,this._suggestedMax=void 0,this._suggestedMin=void 0,this._ticksLength=0,this._borderValue=0,this._cache={},this._dataLimitsCached=!1,this.$context=void 0}init(t){this.options=t.setContext(this.getContext()),this.axis=t.axis,this._userMin=this.parse(t.min),this._userMax=this.parse(t.max),this._suggestedMin=this.parse(t.suggestedMin),this._suggestedMax=this.parse(t.suggestedMax)}parse(t,e){return t}getUserBounds(){let{_userMin:t,_userMax:e,_suggestedMin:i,_suggestedMax:n}=this;return t=Te(t,Number.POSITIVE_INFINITY),e=Te(e,Number.NEGATIVE_INFINITY),i=Te(i,Number.POSITIVE_INFINITY),n=Te(n,Number.NEGATIVE_INFINITY),{min:Te(t,i),max:Te(e,n),minDefined:Ce(t),maxDefined:Ce(e)}}getMinMax(t){let e,{min:i,max:n,minDefined:s,maxDefined:o}=this.getUserBounds();if(s&&o)return{min:i,max:n};const r=this.getMatchingVisibleMetas();for(let a=0,l=r.length;a<l;++a)e=r[a].controller.getMinMax(this,t),s||(i=Math.min(i,e.min)),o||(n=Math.max(n,e.max));return i=o&&i>n?n:i,n=s&&i>n?i:n,{min:Te(i,Te(n,i)),max:Te(n,Te(i,n))}}getPadding(){return{left:this.paddingLeft||0,top:this.paddingTop||0,right:this.paddingRight||0,bottom:this.paddingBottom||0}}getTicks(){return this.ticks}getLabels(){const t=this.chart.data;return this.options.labels||(this.isHorizontal()?t.xLabels:t.yLabels)||t.labels||[]}getLabelItems(t=this.chart.chartArea){return this._labelItems||(this._labelItems=this._computeLabelItems(t))}beforeLayout(){this._cache={},this._dataLimitsCached=!1}beforeUpdate(){Re(this.options.beforeUpdate,[this])}update(t,e,i){const{beginAtZero:n,grace:s,ticks:o}=this.options,r=o.sampleSize;this.beforeUpdate(),this.maxWidth=t,this.maxHeight=e,this._margins=i=Object.assign({left:0,right:0,top:0,bottom:0},i),this.ticks=null,this._labelSizes=null,this._gridLineItems=null,this._labelItems=null,this.beforeSetDimensions(),this.setDimensions(),this.afterSetDimensions(),this._maxLength=this.isHorizontal()?this.width+i.left+i.right:this.height+i.top+i.bottom,this._dataLimitsCached||(this.beforeDataLimits(),this.determineDataLimits(),this.afterDataLimits(),this._range=function(t,e,i){const{min:n,max:s}=t,o=Le(e,(s-n)/2),r=(t,e)=>i&&0===t?0:t+e;return{min:r(n,-Math.abs(o)),max:r(s,o)}}(this,s,n),this._dataLimitsCached=!0),this.beforeBuildTicks(),this.ticks=this.buildTicks()||[],this.afterBuildTicks();const a=r<this.ticks.length;this._convertTicksToLabels(a?Eo(this.ticks,r):this.ticks),this.configure(),this.beforeCalculateLabelRotation(),this.calculateLabelRotation(),this.afterCalculateLabelRotation(),o.display&&(o.autoSkip||"auto"===o.source)&&(this.ticks=function(t,e){const i=t.options.ticks,n=function(t){const e=t.options.offset,i=t._tickSize(),n=t._length/i+(e?0:1),s=t._maxLength/i;return Math.floor(Math.min(n,s))}(t),s=Math.min(i.maxTicksLimit||n,n),o=i.major.enabled?function(t){const e=[];let i,n;for(i=0,n=t.length;i<n;i++)t[i].major&&e.push(i);return e}(e):[],r=o.length,a=o[0],l=o[r-1],h=[];if(r>s)return function(t,e,i,n){let s,o=0,r=i[0];for(n=Math.ceil(n),s=0;s<t.length;s++)s===r&&(e.push(t[s]),o++,r=i[o*n])}(e,h,o,r/s),h;const c=function(t,e,i){const n=function(t){const e=t.length;let i,n;if(e<2)return!1;for(n=t[0],i=1;i<e;++i)if(t[i]-t[i-1]!==n)return!1;return n}(t),s=e.length/i;if(!n)return Math.max(s,1);const o=function(t){const e=[],i=Math.sqrt(t);let n;for(n=1;n<i;n++)t%n===0&&(e.push(n),e.push(t/n));return i===(0|i)&&e.push(i),e.sort((t,e)=>t-e).pop(),e}(n);for(let t=0,e=o.length-1;t<e;t++){const e=o[t];if(e>s)return e}return Math.max(s,1)}(o,e,s);if(r>0){let t,i;const n=r>1?Math.round((l-a)/(r-1)):null;for(Ao(e,h,c,De(n)?0:a-n,a),t=0,i=r-1;t<i;t++)Ao(e,h,c,o[t],o[t+1]);return Ao(e,h,c,l,De(n)?e.length:l+n),h}return Ao(e,h,c),h}(this,this.ticks),this._labelSizes=null,this.afterAutoSkip()),a&&this._convertTicksToLabels(this.ticks),this.beforeFit(),this.fit(),this.afterFit(),this.afterUpdate()}configure(){let t,e,i=this.options.reverse;this.isHorizontal()?(t=this.left,e=this.right):(t=this.top,e=this.bottom,i=!i),this._startPixel=t,this._endPixel=e,this._reversePixels=i,this._length=e-t,this._alignToPixels=this.options.alignToPixels}afterUpdate(){Re(this.options.afterUpdate,[this])}beforeSetDimensions(){Re(this.options.beforeSetDimensions,[this])}setDimensions(){this.isHorizontal()?(this.width=this.maxWidth,this.left=0,this.right=this.width):(this.height=this.maxHeight,this.top=0,this.bottom=this.height),this.paddingLeft=0,this.paddingTop=0,this.paddingRight=0,this.paddingBottom=0}afterSetDimensions(){Re(this.options.afterSetDimensions,[this])}_callHooks(t){this.chart.notifyPlugins(t,this.getContext()),Re(this.options[t],[this])}beforeDataLimits(){this._callHooks("beforeDataLimits")}determineDataLimits(){}afterDataLimits(){this._callHooks("afterDataLimits")}beforeBuildTicks(){this._callHooks("beforeBuildTicks")}buildTicks(){return[]}afterBuildTicks(){this._callHooks("afterBuildTicks")}beforeTickToLabelConversion(){Re(this.options.beforeTickToLabelConversion,[this])}generateTickLabels(t){const e=this.options.ticks;let i,n,s;for(i=0,n=t.length;i<n;i++)s=t[i],s.label=Re(e.callback,[s.value,i,t],this)}afterTickToLabelConversion(){Re(this.options.afterTickToLabelConversion,[this])}beforeCalculateLabelRotation(){Re(this.options.beforeCalculateLabelRotation,[this])}calculateLabelRotation(){const t=this.options,e=t.ticks,i=To(this.ticks.length,t.ticks.maxTicksLimit),n=e.minRotation||0,s=e.maxRotation;let o,r,a,l=n;if(!this._isVisible()||!e.display||n>=s||i<=1||!this.isHorizontal())return void(this.labelRotation=n);const h=this._getLabelSizes(),c=h.widest.width,d=h.highest.height,u=bi(this.chart.width-c,0,this.maxWidth);o=t.offset?this.maxWidth/i:u/(i-1),c+6>o&&(o=u/(i-(t.offset?.5:1)),r=this.maxHeight-Ro(t.grid)-e.padding-Io(t.title,this.chart.options.font),a=Math.sqrt(c*c+d*d),l=ci(Math.min(Math.asin(bi((h.highest.height+6)/o,-1,1)),Math.asin(bi(r/a,-1,1))-Math.asin(bi(d/a,-1,1)))),l=Math.max(n,Math.min(s,l))),this.labelRotation=l}afterCalculateLabelRotation(){Re(this.options.afterCalculateLabelRotation,[this])}afterAutoSkip(){}beforeFit(){Re(this.options.beforeFit,[this])}fit(){const t={width:0,height:0},{chart:e,options:{ticks:i,title:n,grid:s}}=this,o=this._isVisible(),r=this.isHorizontal();if(o){const o=Io(n,e.options.font);if(r?(t.width=this.maxWidth,t.height=Ro(s)+o):(t.height=this.maxHeight,t.width=Ro(s)+o),i.display&&this.ticks.length){const{first:e,last:n,widest:s,highest:o}=this._getLabelSizes(),a=2*i.padding,l=hi(this.labelRotation),h=Math.cos(l),c=Math.sin(l);if(r){const e=i.mirror?0:c*s.width+h*o.height;t.height=Math.min(this.maxHeight,t.height+e+a)}else{const e=i.mirror?0:h*s.width+c*o.height;t.width=Math.min(this.maxWidth,t.width+e+a)}this._calculatePadding(e,n,c,h)}}this._handleMargins(),r?(this.width=this._length=e.width-this._margins.left-this._margins.right,this.height=t.height):(this.width=t.width,this.height=this._length=e.height-this._margins.top-this._margins.bottom)}_calculatePadding(t,e,i,n){const{ticks:{align:s,padding:o},position:r}=this.options,a=0!==this.labelRotation,l="top"!==r&&"x"===this.axis;if(this.isHorizontal()){const r=this.getPixelForTick(0)-this.left,h=this.right-this.getPixelForTick(this.ticks.length-1);let c=0,d=0;a?l?(c=n*t.width,d=i*e.height):(c=i*t.height,d=n*e.width):"start"===s?d=e.width:"end"===s?c=t.width:"inner"!==s&&(c=t.width/2,d=e.width/2),this.paddingLeft=Math.max((c-r+o)*this.width/(this.width-r),0),this.paddingRight=Math.max((d-h+o)*this.width/(this.width-h),0)}else{let i=e.height/2,n=t.height/2;"start"===s?(i=0,n=t.height):"end"===s&&(i=e.height,n=0),this.paddingTop=i+o,this.paddingBottom=n+o}}_handleMargins(){this._margins&&(this._margins.left=Math.max(this.paddingLeft,this._margins.left),this._margins.top=Math.max(this.paddingTop,this._margins.top),this._margins.right=Math.max(this.paddingRight,this._margins.right),this._margins.bottom=Math.max(this.paddingBottom,this._margins.bottom))}afterFit(){Re(this.options.afterFit,[this])}isHorizontal(){const{axis:t,position:e}=this.options;return"top"===e||"bottom"===e||"x"===t}isFullSize(){return this.options.fullSize}_convertTicksToLabels(t){let e,i;for(this.beforeTickToLabelConversion(),this.generateTickLabels(t),e=0,i=t.length;e<i;e++)De(t[e].label)&&(t.splice(e,1),i--,e--);this.afterTickToLabelConversion()}_getLabelSizes(){let t=this._labelSizes;if(!t){const e=this.options.ticks.sampleSize;let i=this.ticks;e<i.length&&(i=Eo(i,e)),this._labelSizes=t=this._computeLabelSizes(i,i.length,this.options.ticks.maxTicksLimit)}return t}_computeLabelSizes(t,e,i){const{ctx:n,_longestTextCache:s}=this,o=[],r=[],a=Math.floor(e/To(e,i));let l,h,c,d,u,f,p,g,m,b,x,y=0,v=0;for(l=0;l<e;l+=a){if(d=t[l].label,u=this._resolveTickFontOptions(l),n.font=f=u.string,p=s[f]=s[f]||{data:{},gc:[]},g=u.lineHeight,m=b=0,De(d)||Pe(d)){if(Pe(d))for(h=0,c=d.length;h<c;++h)x=d[h],De(x)||Pe(x)||(m=Gi(n,p.data,p.gc,m,x),b+=g)}else m=Gi(n,p.data,p.gc,m,d),b=g;o.push(m),r.push(b),y=Math.max(m,y),v=Math.max(b,v)}!function(t,e){Ie(t,t=>{const i=t.gc,n=i.length/2;let s;if(n>e){for(s=0;s<n;++s)delete t.data[i[s]];i.splice(0,n)}})}(s,e);const _=o.indexOf(y),w=r.indexOf(v),M=t=>({width:o[t]||0,height:r[t]||0});return{first:M(0),last:M(e-1),widest:M(_),highest:M(w),widths:o,heights:r}}getLabelForValue(t){return t}getPixelForValue(t,e){return NaN}getValueForPixel(t){}getPixelForTick(t){const e=this.ticks;return t<0||t>e.length-1?null:this.getPixelForValue(e[t].value)}getPixelForDecimal(t){this._reversePixels&&(t=1-t);const e=this._startPixel+t*this._length;return bi(this._alignToPixels?Zi(this.chart,e,0):e,-32768,32767)}getDecimalForPixel(t){const e=(t-this._startPixel)/this._length;return this._reversePixels?1-e:e}getBasePixel(){return this.getPixelForValue(this.getBaseValue())}getBaseValue(){const{min:t,max:e}=this;return t<0&&e<0?e:t>0&&e>0?t:0}getContext(t){const e=this.ticks||[];if(t>=0&&t<e.length){const i=e[t];return i.$context||(i.$context=function(t,e,i){return wn(t,{tick:i,index:e,type:"tick"})}(this.getContext(),t,i))}return this.$context||(this.$context=wn(this.chart.getContext(),{scale:this,type:"scale"}))}_tickSize(){const t=this.options.ticks,e=hi(this.labelRotation),i=Math.abs(Math.cos(e)),n=Math.abs(Math.sin(e)),s=this._getLabelSizes(),o=t.autoSkipPadding||0,r=s?s.widest.width+o:0,a=s?s.highest.height+o:0;return this.isHorizontal()?a*i>r*n?r/i:a/n:a*n<r*i?a/i:r/n}_isVisible(){const t=this.options.display;return"auto"!==t?!!t:this.getMatchingVisibleMetas().length>0}_computeGridLineItems(t){const e=this.axis,i=this.chart,n=this.options,{grid:s,position:o,border:r}=n,a=s.offset,l=this.isHorizontal(),h=this.ticks.length+(a?1:0),c=Ro(s),d=[],u=r.setContext(this.getContext()),f=u.display?u.width:0,p=f/2,g=function(t){return Zi(i,t,f)};let m,b,x,y,v,_,w,M,k,S,O,D;if("top"===o)m=g(this.bottom),_=this.bottom-c,M=m-p,S=g(t.top)+p,D=t.bottom;else if("bottom"===o)m=g(this.top),S=t.top,D=g(t.bottom)-p,_=m+p,M=this.top+c;else if("left"===o)m=g(this.right),v=this.right-c,w=m-p,k=g(t.left)+p,O=t.right;else if("right"===o)m=g(this.left),k=t.left,O=g(t.right)-p,v=m+p,w=this.left+c;else if("x"===e){if("center"===o)m=g((t.top+t.bottom)/2+.5);else if(Ae(o)){const t=Object.keys(o)[0],e=o[t];m=g(this.chart.scales[t].getPixelForValue(e))}S=t.top,D=t.bottom,_=m+p,M=_+c}else if("y"===e){if("center"===o)m=g((t.left+t.right)/2);else if(Ae(o)){const t=Object.keys(o)[0],e=o[t];m=g(this.chart.scales[t].getPixelForValue(e))}v=m-p,w=v-c,k=t.left,O=t.right}const P=Ee(n.ticks.maxTicksLimit,h),A=Math.max(1,Math.ceil(h/P));for(b=0;b<h;b+=A){const t=this.getContext(b),e=s.setContext(t),n=r.setContext(t),o=e.lineWidth,h=e.color,c=n.dash||[],u=n.dashOffset,f=e.tickWidth,p=e.tickColor,g=e.tickBorderDash||[],m=e.tickBorderDashOffset;x=Lo(this,b,a),void 0!==x&&(y=Zi(i,x,o),l?v=w=k=O=y:_=M=S=D=y,d.push({tx1:v,ty1:_,tx2:w,ty2:M,x1:k,y1:S,x2:O,y2:D,width:o,color:h,borderDash:c,borderDashOffset:u,tickWidth:f,tickColor:p,tickBorderDash:g,tickBorderDashOffset:m}))}return this._ticksLength=h,this._borderValue=m,d}_computeLabelItems(t){const e=this.axis,i=this.options,{position:n,ticks:s}=i,o=this.isHorizontal(),r=this.ticks,{align:a,crossAlign:l,padding:h,mirror:c}=s,d=Ro(i.grid),u=d+h,f=c?-h:u,p=-hi(this.labelRotation),g=[];let m,b,x,y,v,_,w,M,k,S,O,D,P="middle";if("top"===n)_=this.bottom-f,w=this._getXAxisLabelAlignment();else if("bottom"===n)_=this.top+f,w=this._getXAxisLabelAlignment();else if("left"===n){const t=this._getYAxisLabelAlignment(d);w=t.textAlign,v=t.x}else if("right"===n){const t=this._getYAxisLabelAlignment(d);w=t.textAlign,v=t.x}else if("x"===e){if("center"===n)_=(t.top+t.bottom)/2+u;else if(Ae(n)){const t=Object.keys(n)[0],e=n[t];_=this.chart.scales[t].getPixelForValue(e)+u}w=this._getXAxisLabelAlignment()}else if("y"===e){if("center"===n)v=(t.left+t.right)/2-u;else if(Ae(n)){const t=Object.keys(n)[0],e=n[t];v=this.chart.scales[t].getPixelForValue(e)}w=this._getYAxisLabelAlignment(d).textAlign}"y"===e&&("start"===a?P="top":"end"===a&&(P="bottom"));const A=this._getLabelSizes();for(m=0,b=r.length;m<b;++m){x=r[m],y=x.label;const t=s.setContext(this.getContext(m));M=this.getPixelForTick(m)+s.labelOffset,k=this._resolveTickFontOptions(m),S=k.lineHeight,O=Pe(y)?y.length:1;const e=O/2,i=t.color,a=t.textStrokeColor,h=t.textStrokeWidth;let d,u=w;if(o?(v=M,"inner"===w&&(u=m===b-1?this.options.reverse?"left":"right":0===m?this.options.reverse?"right":"left":"center"),D="top"===n?"near"===l||0!==p?-O*S+S/2:"center"===l?-A.highest.height/2-e*S+S:-A.highest.height+S/2:"near"===l||0!==p?S/2:"center"===l?A.highest.height/2-e*S:A.highest.height-O*S,c&&(D*=-1),0===p||t.showLabelBackdrop||(v+=S/2*Math.sin(p))):(_=M,D=(1-O)*S/2),t.showLabelBackdrop){const e=yn(t.backdropPadding),i=A.heights[m],n=A.widths[m];let s=D-e.top,o=0-e.left;switch(P){case"middle":s-=i/2;break;case"bottom":s-=i}switch(w){case"center":o-=n/2;break;case"right":o-=n;break;case"inner":m===b-1?o-=n:m>0&&(o-=n/2)}d={left:o,top:s,width:n+e.width,height:i+e.height,color:t.backdropColor}}g.push({label:y,font:k,textOffset:D,options:{rotation:p,color:i,strokeColor:a,strokeWidth:h,textAlign:u,textBaseline:P,translation:[v,_],backdrop:d}})}return g}_getXAxisLabelAlignment(){const{position:t,ticks:e}=this.options;if(-hi(this.labelRotation))return"top"===t?"left":"right";let i="center";return"start"===e.align?i="left":"end"===e.align?i="right":"inner"===e.align&&(i="inner"),i}_getYAxisLabelAlignment(t){const{position:e,ticks:{crossAlign:i,mirror:n,padding:s}}=this.options,o=t+s,r=this._getLabelSizes().widest.width;let a,l;return"left"===e?n?(l=this.right+s,"near"===i?a="left":"center"===i?(a="center",l+=r/2):(a="right",l+=r)):(l=this.right-o,"near"===i?a="right":"center"===i?(a="center",l-=r/2):(a="left",l=this.left)):"right"===e?n?(l=this.left+s,"near"===i?a="right":"center"===i?(a="center",l-=r/2):(a="left",l-=r)):(l=this.left+o,"near"===i?a="left":"center"===i?(a="center",l+=r/2):(a="right",l=this.right)):a="right",{textAlign:a,x:l}}_computeLabelArea(){if(this.options.ticks.mirror)return;const t=this.chart,e=this.options.position;return"left"===e||"right"===e?{top:0,left:this.left,bottom:t.height,right:this.right}:"top"===e||"bottom"===e?{top:this.top,left:0,bottom:this.bottom,right:t.width}:void 0}drawBackground(){const{ctx:t,options:{backgroundColor:e},left:i,top:n,width:s,height:o}=this;e&&(t.save(),t.fillStyle=e,t.fillRect(i,n,s,o),t.restore())}getLineWidthForValue(t){const e=this.options.grid;if(!this._isVisible()||!e.display)return 0;const i=this.ticks.findIndex(e=>e.value===t);return i>=0?e.setContext(this.getContext(i)).lineWidth:0}drawGrid(t){const e=this.options.grid,i=this.ctx,n=this._gridLineItems||(this._gridLineItems=this._computeGridLineItems(t));let s,o;const r=(t,e,n)=>{n.width&&n.color&&(i.save(),i.lineWidth=n.width,i.strokeStyle=n.color,i.setLineDash(n.borderDash||[]),i.lineDashOffset=n.borderDashOffset,i.beginPath(),i.moveTo(t.x,t.y),i.lineTo(e.x,e.y),i.stroke(),i.restore())};if(e.display)for(s=0,o=n.length;s<o;++s){const t=n[s];e.drawOnChartArea&&r({x:t.x1,y:t.y1},{x:t.x2,y:t.y2},t),e.drawTicks&&r({x:t.tx1,y:t.ty1},{x:t.tx2,y:t.ty2},{color:t.tickColor,width:t.tickWidth,borderDash:t.tickBorderDash,borderDashOffset:t.tickBorderDashOffset})}}drawBorder(){const{chart:t,ctx:e,options:{border:i,grid:n}}=this,s=i.setContext(this.getContext()),o=i.display?s.width:0;if(!o)return;const r=n.setContext(this.getContext(0)).lineWidth,a=this._borderValue;let l,h,c,d;this.isHorizontal()?(l=Zi(t,this.left,o)-o/2,h=Zi(t,this.right,r)+r/2,c=d=a):(c=Zi(t,this.top,o)-o/2,d=Zi(t,this.bottom,r)+r/2,l=h=a),e.save(),e.lineWidth=s.width,e.strokeStyle=s.color,e.beginPath(),e.moveTo(l,c),e.lineTo(h,d),e.stroke(),e.restore()}drawLabels(t){if(!this.options.ticks.display)return;const e=this.ctx,i=this._computeLabelArea();i&&sn(e,i);const n=this.getLabelItems(t);for(const t of n){const i=t.options,n=t.font;cn(e,t.label,0,t.textOffset,n,i)}i&&on(e)}drawTitle(){const{ctx:t,options:{position:e,title:i,reverse:n}}=this;if(!i.display)return;const s=vn(i.font),o=yn(i.padding),r=i.align;let a=s.lineHeight/2;"bottom"===e||"center"===e||Ae(e)?(a+=o.bottom,Pe(i.text)&&(a+=s.lineHeight*(i.text.length-1))):a+=o.top;const{titleX:l,titleY:h,maxWidth:c,rotation:d}=function(t,e,i,n){const{top:s,left:o,bottom:r,right:a,chart:l}=t,{chartArea:h,scales:c}=l;let d,u,f,p=0;const g=r-s,m=a-o;if(t.isHorizontal()){if(u=Pi(n,o,a),Ae(i)){const t=Object.keys(i)[0],n=i[t];f=c[t].getPixelForValue(n)+g-e}else f="center"===i?(h.bottom+h.top)/2+g-e:Co(t,i,e);d=a-o}else{if(Ae(i)){const t=Object.keys(i)[0],n=i[t];u=c[t].getPixelForValue(n)-m+e}else u="center"===i?(h.left+h.right)/2-m+e:Co(t,i,e);f=Pi(n,r,s),p="left"===i?-ti:ti}return{titleX:u,titleY:f,maxWidth:d,rotation:p}}(this,a,e,r);cn(t,i.text,0,0,s,{color:i.color,maxWidth:c,rotation:d,textAlign:Fo(r,e,n),textBaseline:"middle",translation:[l,h]})}draw(t){this._isVisible()&&(this.drawBackground(),this.drawGrid(t),this.drawBorder(),this.drawTitle(),this.drawLabels(t))}_layers(){const t=this.options,e=t.ticks&&t.ticks.z||0,i=Ee(t.grid&&t.grid.z,-1),n=Ee(t.border&&t.border.z,0);return this._isVisible()&&this.draw===zo.prototype.draw?[{z:i,draw:t=>{this.drawBackground(),this.drawGrid(t),this.drawTitle()}},{z:n,draw:()=>{this.drawBorder()}},{z:e,draw:t=>{this.drawLabels(t)}}]:[{z:e,draw:t=>{this.draw(t)}}]}getMatchingVisibleMetas(t){const e=this.chart.getSortedVisibleDatasetMetas(),i=this.axis+"AxisID",n=[];let s,o;for(s=0,o=e.length;s<o;++s){const o=e[s];o[i]!==this.id||t&&o.type!==t||n.push(o)}return n}_resolveTickFontOptions(t){return vn(this.options.ticks.setContext(this.getContext(t)).font)}_maxDigits(){const t=this._resolveTickFontOptions(0).lineHeight;return(this.isHorizontal()?this.width:this.height)/t}}class Vo{constructor(t,e,i){this.type=t,this.scope=e,this.override=i,this.items=Object.create(null)}isForType(t){return Object.prototype.isPrototypeOf.call(this.type.prototype,t.prototype)}register(t){const e=Object.getPrototypeOf(t);let i;(function(t){return"id"in t&&"defaults"in t})(e)&&(i=this.register(e));const n=this.items,s=t.id,o=this.scope+"."+s;if(!s)throw new Error("class does not have id: "+t);return s in n||(n[s]=t,function(t,e,i){const n=We(Object.create(null),[i?Ki.get(i):{},Ki.get(e),t.defaults]);Ki.set(e,n),t.defaultRoutes&&function(t,e){Object.keys(e).forEach(i=>{const n=i.split("."),s=n.pop(),o=[t].concat(n).join("."),r=e[i].split("."),a=r.pop(),l=r.join(".");Ki.route(o,s,l,a)})}(e,t.defaultRoutes),t.descriptors&&Ki.describe(e,t.descriptors)}(t,o,i),this.override&&Ki.override(t.id,t.overrides)),o}get(t){return this.items[t]}unregister(t){const e=this.items,i=t.id,n=this.scope;i in e&&delete e[i],n&&i in Ki[n]&&(delete Ki[n][i],this.override&&delete $i[i])}}class Bo{constructor(){this.controllers=new Vo(Ts,"datasets",!0),this.elements=new Vo(Po,"elements"),this.plugins=new Vo(Object,"plugins"),this.scales=new Vo(zo,"scales"),this._typedRegistries=[this.controllers,this.scales,this.elements]}add(...t){this._each("register",t)}remove(...t){this._each("unregister",t)}addControllers(...t){this._each("register",t,this.controllers)}addElements(...t){this._each("register",t,this.elements)}addPlugins(...t){this._each("register",t,this.plugins)}addScales(...t){this._each("register",t,this.scales)}getController(t){return this._get(t,this.controllers,"controller")}getElement(t){return this._get(t,this.elements,"element")}getPlugin(t){return this._get(t,this.plugins,"plugin")}getScale(t){return this._get(t,this.scales,"scale")}removeControllers(...t){this._each("unregister",t,this.controllers)}removeElements(...t){this._each("unregister",t,this.elements)}removePlugins(...t){this._each("unregister",t,this.plugins)}removeScales(...t){this._each("unregister",t,this.scales)}_each(t,e,i){[...e].forEach(e=>{const n=i||this._getRegistryForType(e);i||n.isForType(e)||n===this.plugins&&e.id?this._exec(t,n,e):Ie(e,e=>{const n=i||this._getRegistryForType(e);this._exec(t,n,e)})})}_exec(t,e,i){const n=Ue(t);Re(i["before"+n],[],i),e[t](i),Re(i["after"+n],[],i)}_getRegistryForType(t){for(let e=0;e<this._typedRegistries.length;e++){const i=this._typedRegistries[e];if(i.isForType(t))return i}return this.plugins}_get(t,e,i){const n=e.get(t);if(void 0===n)throw new Error('"'+t+'" is not a registered '+i+".");return n}}var Wo=new Bo;class jo{constructor(){this._init=[]}notify(t,e,i,n){"beforeInit"===e&&(this._init=this._createDescriptors(t,!0),this._notify(this._init,t,"install"));const s=n?this._descriptors(t).filter(n):this._descriptors(t),o=this._notify(s,t,e,i);return"afterDestroy"===e&&(this._notify(s,t,"stop"),this._notify(this._init,t,"uninstall")),o}_notify(t,e,i,n){n=n||{};for(const s of t){const t=s.plugin;if(!1===Re(t[i],[e,n,s.options],t)&&n.cancelable)return!1}return!0}invalidate(){De(this._cache)||(this._oldCache=this._cache,this._cache=void 0)}_descriptors(t){if(this._cache)return this._cache;const e=this._cache=this._createDescriptors(t);return this._notifyStateChanges(t),e}_createDescriptors(t,e){const i=t&&t.config,n=Ee(i.options&&i.options.plugins,{}),s=function(t){const e={},i=[],n=Object.keys(Wo.plugins.items);for(let t=0;t<n.length;t++)i.push(Wo.getPlugin(n[t]));const s=t.plugins||[];for(let t=0;t<s.length;t++){const n=s[t];-1===i.indexOf(n)&&(i.push(n),e[n.id]=!0)}return{plugins:i,localIds:e}}(i);return!1!==n||e?function(t,{plugins:e,localIds:i},n,s){const o=[],r=t.getContext();for(const a of e){const e=a.id,l=No(n[e],s);null!==l&&o.push({plugin:a,options:Ho(t.config,{plugin:a,local:i[e]},l,r)})}return o}(t,s,n,e):[]}_notifyStateChanges(t){const e=this._oldCache||[],i=this._cache,n=(t,e)=>t.filter(t=>!e.some(e=>t.plugin.id===e.plugin.id));this._notify(n(e,i),t,"stop"),this._notify(n(i,e),t,"start")}}function No(t,e){return e||!1!==t?!0===t?{}:t:null}function Ho(t,{plugin:e,local:i},n,s){const o=t.pluginScopeKeys(e),r=t.getOptionScopes(n,o);return i&&e.defaults&&r.push(e.defaults),t.createResolver(r,s,[""],{scriptable:!1,indexable:!1,allKeys:!0})}function $o(t,e){const i=Ki.datasets[t]||{};return((e.datasets||{})[t]||{}).indexAxis||e.indexAxis||i.indexAxis||"x"}function Uo(t){if("x"===t||"y"===t||"r"===t)return t}function Yo(t){return"top"===t||"bottom"===t?"x":"left"===t||"right"===t?"y":void 0}function Xo(t,...e){if(Uo(t))return t;for(const i of e){const e=i.axis||Yo(i.position)||t.length>1&&Uo(t[0].toLowerCase());if(e)return e}throw new Error(`Cannot determine type of '${t}' axis. Please provide 'axis' or 'position' option.`)}function qo(t,e,i){if(i[e+"AxisID"]===t)return{axis:e}}function Ko(t){const e=t.options||(t.options={});e.plugins=Ee(e.plugins,{}),e.scales=function(t,e){const i=$i[t.type]||{scales:{}},n=e.scales||{},s=$o(t.type,e),o=Object.create(null);return Object.keys(n).forEach(e=>{const r=n[e];if(!Ae(r))return console.error(`Invalid scale configuration for scale: ${e}`);if(r._proxy)return console.warn(`Ignoring resolver passed as options for scale: ${e}`);const a=Xo(e,r,function(t,e){if(e.data&&e.data.datasets){const i=e.data.datasets.filter(e=>e.xAxisID===t||e.yAxisID===t);if(i.length)return qo(t,"x",i[0])||qo(t,"y",i[0])}return{}}(e,t),Ki.scales[r.type]),l=function(t,e){return t===e?"_index_":"_value_"}(a,s),h=i.scales||{};o[e]=je(Object.create(null),[{axis:a},r,h[a],h[l]])}),t.data.datasets.forEach(i=>{const s=i.type||t.type,r=i.indexAxis||$o(s,e),a=($i[s]||{}).scales||{};Object.keys(a).forEach(t=>{const e=function(t,e){let i=t;return"_index_"===t?i=e:"_value_"===t&&(i="x"===e?"y":"x"),i}(t,r),s=i[e+"AxisID"]||e;o[s]=o[s]||Object.create(null),je(o[s],[{axis:e},n[s],a[t]])})}),Object.keys(o).forEach(t=>{const e=o[t];je(e,[Ki.scales[e.type],Ki.scale])}),o}(t,e)}function Go(t){return(t=t||{}).datasets=t.datasets||[],t.labels=t.labels||[],t}const Jo=new Map,Zo=new Set;function Qo(t,e){let i=Jo.get(t);return i||(i=e(),Jo.set(t,i),Zo.add(i)),i}const tr=(t,e,i)=>{const n=$e(e,i);void 0!==n&&t.add(n)};class er{constructor(t){this._config=function(t){return(t=t||{}).data=Go(t.data),Ko(t),t}(t),this._scopeCache=new Map,this._resolverCache=new Map}get platform(){return this._config.platform}get type(){return this._config.type}set type(t){this._config.type=t}get data(){return this._config.data}set data(t){this._config.data=Go(t)}get options(){return this._config.options}set options(t){this._config.options=t}get plugins(){return this._config.plugins}update(){const t=this._config;this.clearCache(),Ko(t)}clearCache(){this._scopeCache.clear(),this._resolverCache.clear()}datasetScopeKeys(t){return Qo(t,()=>[[`datasets.${t}`,""]])}datasetAnimationScopeKeys(t,e){return Qo(`${t}.transition.${e}`,()=>[[`datasets.${t}.transitions.${e}`,`transitions.${e}`],[`datasets.${t}`,""]])}datasetElementScopeKeys(t,e){return Qo(`${t}-${e}`,()=>[[`datasets.${t}.elements.${e}`,`datasets.${t}`,`elements.${e}`,""]])}pluginScopeKeys(t){const e=t.id;return Qo(`${this.type}-plugin-${e}`,()=>[[`plugins.${e}`,...t.additionalOptionScopes||[]]])}_cachedScopes(t,e){const i=this._scopeCache;let n=i.get(t);return n&&!e||(n=new Map,i.set(t,n)),n}getOptionScopes(t,e,i){const{options:n,type:s}=this,o=this._cachedScopes(t,i),r=o.get(e);if(r)return r;const a=new Set;e.forEach(e=>{t&&(a.add(t),e.forEach(e=>tr(a,t,e))),e.forEach(t=>tr(a,n,t)),e.forEach(t=>tr(a,$i[s]||{},t)),e.forEach(t=>tr(a,Ki,t)),e.forEach(t=>tr(a,Ui,t))});const l=Array.from(a);return 0===l.length&&l.push(Object.create(null)),Zo.has(e)&&o.set(e,l),l}chartOptionScopes(){const{options:t,type:e}=this;return[t,$i[e]||{},Ki.datasets[e]||{},{type:e},Ki,Ui]}resolveNamedOptions(t,e,i,n=[""]){const s={$shared:!0},{resolver:o,subPrefixes:r}=ir(this._resolverCache,t,n);let a=o;(function(t,e){const{isScriptable:i,isIndexable:n}=Sn(t);for(const s of e){const e=i(s),o=n(s),r=(o||e)&&t[s];if(e&&(Xe(r)||nr(r))||o&&Pe(r))return!0}return!1})(o,e)&&(s.$shared=!1,a=kn(o,i=Xe(i)?i():i,this.createResolver(t,i,r)));for(const t of e)s[t]=a[t];return s}createResolver(t,e,i=[""],n){const{resolver:s}=ir(this._resolverCache,t,i);return Ae(e)?kn(s,e,void 0,n):s}}function ir(t,e,i){let n=t.get(e);n||(n=new Map,t.set(e,n));const s=i.join();let o=n.get(s);return o||(o={resolver:Mn(e,i),subPrefixes:i.filter(t=>!t.toLowerCase().includes("hover"))},n.set(s,o)),o}const nr=t=>Ae(t)&&Object.getOwnPropertyNames(t).some(e=>Xe(t[e])),sr=["top","bottom","left","right","chartArea"];function or(t,e){return"top"===t||"bottom"===t||-1===sr.indexOf(t)&&"x"===e}function rr(t,e){return function(i,n){return i[t]===n[t]?i[e]-n[e]:i[t]-n[t]}}function ar(t){const e=t.chart,i=e.options.animation;e.notifyPlugins("afterRender"),Re(i&&i.onComplete,[t],e)}function lr(t){const e=t.chart,i=e.options.animation;Re(i&&i.onProgress,[t],e)}function hr(t){return Hn()&&"string"==typeof t?t=document.getElementById(t):t&&t.length&&(t=t[0]),t&&t.canvas&&(t=t.canvas),t}const cr={},dr=t=>{const e=hr(t);return Object.values(cr).filter(t=>t.canvas===e).pop()};function ur(t,e,i){const n=Object.keys(t);for(const s of n){const n=+s;if(n>=e){const o=t[s];delete t[s],(i>0||n>e)&&(t[n+i]=o)}}}class fr{static defaults=Ki;static instances=cr;static overrides=$i;static registry=Wo;static version="4.5.0";static getChart=dr;static register(...t){Wo.add(...t),pr()}static unregister(...t){Wo.remove(...t),pr()}constructor(t,e){const i=this.config=new er(e),n=hr(t),s=dr(n);if(s)throw new Error("Canvas is already in use. Chart with ID '"+s.id+"' must be destroyed before the canvas with ID '"+s.canvas.id+"' can be reused.");const o=i.createResolver(i.chartOptionScopes(),this.getContext());this.platform=new(i.platform||function(t){return!Hn()||"undefined"!=typeof OffscreenCanvas&&t instanceof OffscreenCanvas?uo:Do}(n)),this.platform.updateConfig(i);const r=this.platform.acquireContext(n,o.aspectRatio),a=r&&r.canvas,l=a&&a.height,h=a&&a.width;this.id=Oe(),this.ctx=r,this.canvas=a,this.width=h,this.height=l,this._options=o,this._aspectRatio=this.aspectRatio,this._layers=[],this._metasets=[],this._stacks=void 0,this.boxes=[],this.currentDevicePixelRatio=void 0,this.chartArea=void 0,this._active=[],this._lastEvent=void 0,this._listeners={},this._responsiveListeners=void 0,this._sortedMetasets=[],this.scales={},this._plugins=new jo,this.$proxies={},this._hiddenIndices={},this.attached=!1,this._animationsDisabled=void 0,this.$context=void 0,this._doResize=function(t,e){let i;return function(...n){return e?(clearTimeout(i),i=setTimeout(t,e,n)):t.apply(this,n),e}}(t=>this.update(t),o.resizeDelay||0),this._dataChanges=[],cr[this.id]=this,r&&a?(gs.listen(this,"complete",ar),gs.listen(this,"progress",lr),this._initialize(),this.attached&&this.update()):console.error("Failed to create chart: can't acquire context from the given item")}get aspectRatio(){const{options:{aspectRatio:t,maintainAspectRatio:e},width:i,height:n,_aspectRatio:s}=this;return De(t)?e&&s?s:n?i/n:null:t}get data(){return this.config.data}set data(t){this.config.data=t}get options(){return this._options}set options(t){this.config.options=t}get registry(){return Wo}_initialize(){return this.notifyPlugins("beforeInit"),this.options.responsive?this.resize():Jn(this,this.options.devicePixelRatio),this.bindEvents(),this.notifyPlugins("afterInit"),this}clear(){return Qi(this.canvas,this.ctx),this}stop(){return gs.stop(this),this}resize(t,e){gs.running(this)?this._resizeBeforeDraw={width:t,height:e}:this._resize(t,e)}_resize(t,e){const i=this.options,n=this.canvas,s=i.maintainAspectRatio&&this.aspectRatio,o=this.platform.getMaximumSize(n,t,e,s),r=i.devicePixelRatio||this.platform.getDevicePixelRatio(),a=this.width?"resize":"attach";this.width=o.width,this.height=o.height,this._aspectRatio=this.aspectRatio,Jn(this,r,!0)&&(this.notifyPlugins("resize",{size:o}),Re(i.onResize,[this,o],this),this.attached&&this._doResize(a)&&this.render())}ensureScalesHaveIDs(){Ie(this.options.scales||{},(t,e)=>{t.id=e})}buildOrUpdateScales(){const t=this.options,e=t.scales,i=this.scales,n=Object.keys(i).reduce((t,e)=>(t[e]=!1,t),{});let s=[];e&&(s=s.concat(Object.keys(e).map(t=>{const i=e[t],n=Xo(t,i),s="r"===n,o="x"===n;return{options:i,dposition:s?"chartArea":o?"bottom":"left",dtype:s?"radialLinear":o?"category":"linear"}}))),Ie(s,e=>{const s=e.options,o=s.id,r=Xo(o,s),a=Ee(s.type,e.dtype);void 0!==s.position&&or(s.position,r)===or(e.dposition)||(s.position=e.dposition),n[o]=!0;let l=null;o in i&&i[o].type===a?l=i[o]:(l=new(Wo.getScale(a))({id:o,type:a,ctx:this.ctx,chart:this}),i[l.id]=l),l.init(s,t)}),Ie(n,(t,e)=>{t||delete i[e]}),Ie(i,t=>{ho.configure(this,t,t.options),ho.addBox(this,t)})}_updateMetasets(){const t=this._metasets,e=this.data.datasets.length,i=t.length;if(t.sort((t,e)=>t.index-e.index),i>e){for(let t=e;t<i;++t)this._destroyDatasetMeta(t);t.splice(e,i-e)}this._sortedMetasets=t.slice(0).sort(rr("order","index"))}_removeUnreferencedMetasets(){const{_metasets:t,data:{datasets:e}}=this;t.length>e.length&&delete this._stacks,t.forEach((t,i)=>{0===e.filter(e=>e===t._dataset).length&&this._destroyDatasetMeta(i)})}buildOrUpdateControllers(){const t=[],e=this.data.datasets;let i,n;for(this._removeUnreferencedMetasets(),i=0,n=e.length;i<n;i++){const n=e[i];let s=this.getDatasetMeta(i);const o=n.type||this.config.type;if(s.type&&s.type!==o&&(this._destroyDatasetMeta(i),s=this.getDatasetMeta(i)),s.type=o,s.indexAxis=n.indexAxis||$o(o,this.options),s.order=n.order||0,s.index=i,s.label=""+n.label,s.visible=this.isDatasetVisible(i),s.controller)s.controller.updateIndex(i),s.controller.linkScales();else{const e=Wo.getController(o),{datasetElementType:n,dataElementType:r}=Ki.datasets[o];Object.assign(e,{dataElementType:Wo.getElement(r),datasetElementType:n&&Wo.getElement(n)}),s.controller=new e(this,i),t.push(s.controller)}}return this._updateMetasets(),t}_resetElements(){Ie(this.data.datasets,(t,e)=>{this.getDatasetMeta(e).controller.reset()},this)}reset(){this._resetElements(),this.notifyPlugins("reset")}update(t){const e=this.config;e.update();const i=this._options=e.createResolver(e.chartOptionScopes(),this.getContext()),n=this._animationsDisabled=!i.animation;if(this._updateScales(),this._checkEventBindings(),this._updateHiddenIndices(),this._plugins.invalidate(),!1===this.notifyPlugins("beforeUpdate",{mode:t,cancelable:!0}))return;const s=this.buildOrUpdateControllers();this.notifyPlugins("beforeElementsUpdate");let o=0;for(let t=0,e=this.data.datasets.length;t<e;t++){const{controller:e}=this.getDatasetMeta(t),i=!n&&-1===s.indexOf(e);e.buildOrUpdateElements(i),o=Math.max(+e.getMaxOverflow(),o)}o=this._minPadding=i.layout.autoPadding?o:0,this._updateLayout(o),n||Ie(s,t=>{t.reset()}),this._updateDatasets(t),this.notifyPlugins("afterUpdate",{mode:t}),this._layers.sort(rr("z","_idx"));const{_active:r,_lastEvent:a}=this;a?this._eventHandler(a,!0):r.length&&this._updateHoverStyles(r,r,!0),this.render()}_updateScales(){Ie(this.scales,t=>{ho.removeBox(this,t)}),this.ensureScalesHaveIDs(),this.buildOrUpdateScales()}_checkEventBindings(){const t=this.options,e=new Set(Object.keys(this._listeners)),i=new Set(t.events);qe(e,i)&&!!this._responsiveListeners===t.responsive||(this.unbindEvents(),this.bindEvents())}_updateHiddenIndices(){const{_hiddenIndices:t}=this,e=this._getUniformDataChanges()||[];for(const{method:i,start:n,count:s}of e)ur(t,n,"_removeElements"===i?-s:s)}_getUniformDataChanges(){const t=this._dataChanges;if(!t||!t.length)return;this._dataChanges=[];const e=this.data.datasets.length,i=e=>new Set(t.filter(t=>t[0]===e).map((t,e)=>e+","+t.splice(1).join(","))),n=i(0);for(let t=1;t<e;t++)if(!qe(n,i(t)))return;return Array.from(n).map(t=>t.split(",")).map(t=>({method:t[1],start:+t[2],count:+t[3]}))}_updateLayout(t){if(!1===this.notifyPlugins("beforeLayout",{cancelable:!0}))return;ho.update(this,this.width,this.height,t);const e=this.chartArea,i=e.width<=0||e.height<=0;this._layers=[],Ie(this.boxes,t=>{i&&"chartArea"===t.position||(t.configure&&t.configure(),this._layers.push(...t._layers()))},this),this._layers.forEach((t,e)=>{t._idx=e}),this.notifyPlugins("afterLayout")}_updateDatasets(t){if(!1!==this.notifyPlugins("beforeDatasetsUpdate",{mode:t,cancelable:!0})){for(let t=0,e=this.data.datasets.length;t<e;++t)this.getDatasetMeta(t).controller.configure();for(let e=0,i=this.data.datasets.length;e<i;++e)this._updateDataset(e,Xe(t)?t({datasetIndex:e}):t);this.notifyPlugins("afterDatasetsUpdate",{mode:t})}}_updateDataset(t,e){const i=this.getDatasetMeta(t),n={meta:i,index:t,mode:e,cancelable:!0};!1!==this.notifyPlugins("beforeDatasetUpdate",n)&&(i.controller._update(e),n.cancelable=!1,this.notifyPlugins("afterDatasetUpdate",n))}render(){!1!==this.notifyPlugins("beforeRender",{cancelable:!0})&&(gs.has(this)?this.attached&&!gs.running(this)&&gs.start(this):(this.draw(),ar({chart:this})))}draw(){let t;if(this._resizeBeforeDraw){const{width:t,height:e}=this._resizeBeforeDraw;this._resizeBeforeDraw=null,this._resize(t,e)}if(this.clear(),this.width<=0||this.height<=0)return;if(!1===this.notifyPlugins("beforeDraw",{cancelable:!0}))return;const e=this._layers;for(t=0;t<e.length&&e[t].z<=0;++t)e[t].draw(this.chartArea);for(this._drawDatasets();t<e.length;++t)e[t].draw(this.chartArea);this.notifyPlugins("afterDraw")}_getSortedDatasetMetas(t){const e=this._sortedMetasets,i=[];let n,s;for(n=0,s=e.length;n<s;++n){const s=e[n];t&&!s.visible||i.push(s)}return i}getSortedVisibleDatasetMetas(){return this._getSortedDatasetMetas(!0)}_drawDatasets(){if(!1===this.notifyPlugins("beforeDatasetsDraw",{cancelable:!0}))return;const t=this.getSortedVisibleDatasetMetas();for(let e=t.length-1;e>=0;--e)this._drawDataset(t[e]);this.notifyPlugins("afterDatasetsDraw")}_drawDataset(t){const e=this.ctx,i={meta:t,index:t.index,cancelable:!0},n=fs(this,t);!1!==this.notifyPlugins("beforeDatasetDraw",i)&&(n&&sn(e,n),t.controller.draw(),n&&on(e),i.cancelable=!1,this.notifyPlugins("afterDatasetDraw",i))}isPointInArea(t){return nn(t,this.chartArea,this._minPadding)}getElementsAtEventForMode(t,e,i,n){const s=Js.modes[e];return"function"==typeof s?s(this,t,i,n):[]}getDatasetMeta(t){const e=this.data.datasets[t],i=this._metasets;let n=i.filter(t=>t&&t._dataset===e).pop();return n||(n={type:null,data:[],dataset:null,controller:null,hidden:null,xAxisID:null,yAxisID:null,order:e&&e.order||0,index:t,_dataset:e,_parsed:[],_sorted:!1},i.push(n)),n}getContext(){return this.$context||(this.$context=wn(null,{chart:this,type:"chart"}))}getVisibleDatasetCount(){return this.getSortedVisibleDatasetMetas().length}isDatasetVisible(t){const e=this.data.datasets[t];if(!e)return!1;const i=this.getDatasetMeta(t);return"boolean"==typeof i.hidden?!i.hidden:!e.hidden}setDatasetVisibility(t,e){this.getDatasetMeta(t).hidden=!e}toggleDataVisibility(t){this._hiddenIndices[t]=!this._hiddenIndices[t]}getDataVisibility(t){return!this._hiddenIndices[t]}_updateVisibility(t,e,i){const n=i?"show":"hide",s=this.getDatasetMeta(t),o=s.controller._resolveAnimations(void 0,n);Ye(e)?(s.data[e].hidden=!i,this.update()):(this.setDatasetVisibility(t,i),o.update(s,{visible:i}),this.update(e=>e.datasetIndex===t?n:void 0))}hide(t,e){this._updateVisibility(t,e,!1)}show(t,e){this._updateVisibility(t,e,!0)}_destroyDatasetMeta(t){const e=this._metasets[t];e&&e.controller&&e.controller._destroy(),delete this._metasets[t]}_stop(){let t,e;for(this.stop(),gs.remove(this),t=0,e=this.data.datasets.length;t<e;++t)this._destroyDatasetMeta(t)}destroy(){this.notifyPlugins("beforeDestroy");const{canvas:t,ctx:e}=this;this._stop(),this.config.clearCache(),t&&(this.unbindEvents(),Qi(t,e),this.platform.releaseContext(e),this.canvas=null,this.ctx=null),delete cr[this.id],this.notifyPlugins("afterDestroy")}toBase64Image(...t){return this.canvas.toDataURL(...t)}bindEvents(){this.bindUserEvents(),this.options.responsive?this.bindResponsiveEvents():this.attached=!0}bindUserEvents(){const t=this._listeners,e=this.platform,i=(i,n)=>{e.addEventListener(this,i,n),t[i]=n},n=(t,e,i)=>{t.offsetX=e,t.offsetY=i,this._eventHandler(t)};Ie(this.options.events,t=>i(t,n))}bindResponsiveEvents(){this._responsiveListeners||(this._responsiveListeners={});const t=this._responsiveListeners,e=this.platform,i=(i,n)=>{e.addEventListener(this,i,n),t[i]=n},n=(i,n)=>{t[i]&&(e.removeEventListener(this,i,n),delete t[i])},s=(t,e)=>{this.canvas&&this.resize(t,e)};let o;const r=()=>{n("attach",r),this.attached=!0,this.resize(),i("resize",s),i("detach",o)};o=()=>{this.attached=!1,n("resize",s),this._stop(),this._resize(0,0),i("attach",r)},e.isAttached(this.canvas)?r():o()}unbindEvents(){Ie(this._listeners,(t,e)=>{this.platform.removeEventListener(this,e,t)}),this._listeners={},Ie(this._responsiveListeners,(t,e)=>{this.platform.removeEventListener(this,e,t)}),this._responsiveListeners=void 0}updateHoverStyle(t,e,i){const n=i?"set":"remove";let s,o,r,a;for("dataset"===e&&(s=this.getDatasetMeta(t[0].datasetIndex),s.controller["_"+n+"DatasetHoverStyle"]()),r=0,a=t.length;r<a;++r){o=t[r];const e=o&&this.getDatasetMeta(o.datasetIndex).controller;e&&e[n+"HoverStyle"](o.element,o.datasetIndex,o.index)}}getActiveElements(){return this._active||[]}setActiveElements(t){const e=this._active||[],i=t.map(({datasetIndex:t,index:e})=>{const i=this.getDatasetMeta(t);if(!i)throw new Error("No dataset found at index "+t);return{datasetIndex:t,element:i.data[e],index:e}});!Fe(i,e)&&(this._active=i,this._lastEvent=null,this._updateHoverStyles(i,e))}notifyPlugins(t,e,i){return this._plugins.notify(this,t,e,i)}isPluginEnabled(t){return 1===this._plugins._cache.filter(e=>e.plugin.id===t).length}_updateHoverStyles(t,e,i){const n=this.options.hover,s=(t,e)=>t.filter(t=>!e.some(e=>t.datasetIndex===e.datasetIndex&&t.index===e.index)),o=s(e,t),r=i?t:s(t,e);o.length&&this.updateHoverStyle(o,n.mode,!1),r.length&&n.mode&&this.updateHoverStyle(r,n.mode,!0)}_eventHandler(t,e){const i={event:t,replay:e,cancelable:!0,inChartArea:this.isPointInArea(t)},n=e=>(e.options.events||this.options.events).includes(t.native.type);if(!1===this.notifyPlugins("beforeEvent",i,n))return;const s=this._handleEvent(t,e,i.inChartArea);return i.cancelable=!1,this.notifyPlugins("afterEvent",i,n),(s||i.changed)&&this.render(),this}_handleEvent(t,e,i){const{_active:n=[],options:s}=this,o=e,r=this._getActiveElements(t,n,i,o),a=function(t){return"mouseup"===t.type||"click"===t.type||"contextmenu"===t.type}(t),l=function(t,e,i,n){return i&&"mouseout"!==t.type?n?e:t:null}(t,this._lastEvent,i,a);i&&(this._lastEvent=null,Re(s.onHover,[t,r,this],this),a&&Re(s.onClick,[t,r,this],this));const h=!Fe(r,n);return(h||e)&&(this._active=r,this._updateHoverStyles(r,n,e)),this._lastEvent=l,h}_getActiveElements(t,e,i,n){if("mouseout"===t.type)return[];if(!i)return e;const s=this.options.hover;return this.getElementsAtEventForMode(t,s.mode,s,n)}}function pr(){return Ie(fr.instances,t=>t._plugins.invalidate())}function gr(t,e,i,n){return{x:i+t*Math.cos(e),y:n+t*Math.sin(e)}}function mr(t,e,i,n,s,o){const{x:r,y:a,startAngle:l,pixelMargin:h,innerRadius:c}=e,d=Math.max(e.outerRadius+n+i-h,0),u=c>0?c+n+i+h:0;let f=0;const p=s-l;if(n){const t=((c>0?c-n:0)+(d>0?d-n:0))/2;f=(p-(0!==t?p*t/(t+n):p))/2}const g=(p-Math.max(.001,p*d-i/Ke)/d)/2,m=l+g+f,b=s-g-f,{outerStart:x,outerEnd:y,innerStart:v,innerEnd:_}=function(t,e,i,n){const s=mn(t.options.borderRadius,["outerStart","outerEnd","innerStart","innerEnd"]),o=(i-e)/2,r=Math.min(o,n*e/2),a=t=>{const e=(i-Math.min(o,t))*n/2;return bi(t,0,Math.min(o,e))};return{outerStart:a(s.outerStart),outerEnd:a(s.outerEnd),innerStart:bi(s.innerStart,0,r),innerEnd:bi(s.innerEnd,0,r)}}(e,u,d,b-m),w=d-x,M=d-y,k=m+x/w,S=b-y/M,O=u+v,D=u+_,P=m+v/O,A=b-_/D;if(t.beginPath(),o){const e=(k+S)/2;if(t.arc(r,a,d,k,e),t.arc(r,a,d,e,S),y>0){const e=gr(M,S,r,a);t.arc(e.x,e.y,y,S,b+ti)}const i=gr(D,b,r,a);if(t.lineTo(i.x,i.y),_>0){const e=gr(D,A,r,a);t.arc(e.x,e.y,_,b+ti,A+Math.PI)}const n=(b-_/u+(m+v/u))/2;if(t.arc(r,a,u,b-_/u,n,!0),t.arc(r,a,u,n,m+v/u,!0),v>0){const e=gr(O,P,r,a);t.arc(e.x,e.y,v,P+Math.PI,m-ti)}const s=gr(w,m,r,a);if(t.lineTo(s.x,s.y),x>0){const e=gr(w,k,r,a);t.arc(e.x,e.y,x,m-ti,k)}}else{t.moveTo(r,a);const e=Math.cos(k)*d+r,i=Math.sin(k)*d+a;t.lineTo(e,i);const n=Math.cos(S)*d+r,s=Math.sin(S)*d+a;t.lineTo(n,s)}t.closePath()}function br(t,e,i=e){t.lineCap=Ee(i.borderCapStyle,e.borderCapStyle),t.setLineDash(Ee(i.borderDash,e.borderDash)),t.lineDashOffset=Ee(i.borderDashOffset,e.borderDashOffset),t.lineJoin=Ee(i.borderJoinStyle,e.borderJoinStyle),t.lineWidth=Ee(i.borderWidth,e.borderWidth),t.strokeStyle=Ee(i.borderColor,e.borderColor)}function xr(t,e,i){t.lineTo(i.x,i.y)}function yr(t,e,i={}){const n=t.length,{start:s=0,end:o=n-1}=i,{start:r,end:a}=e,l=Math.max(s,r),h=Math.min(o,a),c=s<r&&o<r||s>a&&o>a;return{count:n,start:l,loop:e.loop,ilen:h<l&&!c?n+h-l:h-l}}function vr(t,e,i,n){const{points:s,options:o}=e,{count:r,start:a,loop:l,ilen:h}=yr(s,i,n),c=function(t){return t.stepped?rn:t.tension||"monotone"===t.cubicInterpolationMode?an:xr}(o);let d,u,f,{move:p=!0,reverse:g}=n||{};for(d=0;d<=h;++d)u=s[(a+(g?h-d:d))%r],u.skip||(p?(t.moveTo(u.x,u.y),p=!1):c(t,f,u,g,o.stepped),f=u);return l&&(u=s[(a+(g?h:0))%r],c(t,f,u,g,o.stepped)),!!l}function _r(t,e,i,n){const s=e.points,{count:o,start:r,ilen:a}=yr(s,i,n),{move:l=!0,reverse:h}=n||{};let c,d,u,f,p,g,m=0,b=0;const x=t=>(r+(h?a-t:t))%o,y=()=>{f!==p&&(t.lineTo(m,p),t.lineTo(m,f),t.lineTo(m,g))};for(l&&(d=s[x(0)],t.moveTo(d.x,d.y)),c=0;c<=a;++c){if(d=s[x(c)],d.skip)continue;const e=d.x,i=d.y,n=0|e;n===u?(i<f?f=i:i>p&&(p=i),m=(b*m+e)/++b):(y(),t.lineTo(e,i),u=n,b=0,f=p=i),g=i}y()}function wr(t){const e=t.options,i=e.borderDash&&e.borderDash.length;return t._decimated||t._loop||e.tension||"monotone"===e.cubicInterpolationMode||e.stepped||i?vr:_r}const Mr="function"==typeof Path2D;class kr extends Po{static id="line";static defaults={borderCapStyle:"butt",borderDash:[],borderDashOffset:0,borderJoinStyle:"miter",borderWidth:3,capBezierPoints:!0,cubicInterpolationMode:"default",fill:!1,spanGaps:!1,stepped:!1,tension:0};static defaultRoutes={backgroundColor:"backgroundColor",borderColor:"borderColor"};static descriptors={_scriptable:!0,_indexable:t=>"borderDash"!==t&&"fill"!==t};constructor(t){super(),this.animated=!0,this.options=void 0,this._chart=void 0,this._loop=void 0,this._fullLoop=void 0,this._path=void 0,this._points=void 0,this._segments=void 0,this._decimated=!1,this._pointsUpdated=!1,this._datasetIndex=void 0,t&&Object.assign(this,t)}updateControlPoints(t,e){const i=this.options;if((i.tension||"monotone"===i.cubicInterpolationMode)&&!i.stepped&&!this._pointsUpdated){const n=i.spanGaps?this._loop:this._fullLoop;Nn(this._points,i,t,n,e),this._pointsUpdated=!0}}set points(t){this._points=t,delete this._segments,delete this._path,this._pointsUpdated=!1}get points(){return this._points}get segments(){return this._segments||(this._segments=function(t,e){const i=t.points,n=t.options.spanGaps,s=i.length;if(!s)return[];const o=!!t._loop,{start:r,end:a}=function(t,e,i,n){let s=0,o=e-1;if(i&&!n)for(;s<e&&!t[s].skip;)s++;for(;s<e&&t[s].skip;)s++;for(s%=e,i&&(o+=s);o>s&&t[o%e].skip;)o--;return o%=e,{start:s,end:o}}(i,s,o,n);return function(t,e,i,n){return n&&n.setContext&&i?function(t,e,i,n){const s=t._chart.getContext(),o=cs(t.options),{_datasetIndex:r,options:{spanGaps:a}}=t,l=i.length,h=[];let c=o,d=e[0].start,u=d;function f(t,e,n,s){const o=a?-1:1;if(t!==e){for(t+=l;i[t%l].skip;)t-=o;for(;i[e%l].skip;)e+=o;t%l!==e%l&&(h.push({start:t%l,end:e%l,loop:n,style:s}),c=s,d=e%l)}}for(const t of e){d=a?d:t.start;let e,o=i[d%l];for(u=d+1;u<=t.end;u++){const a=i[u%l];e=cs(n.setContext(wn(s,{type:"segment",p0:o,p1:a,p0DataIndex:(u-1)%l,p1DataIndex:u%l,datasetIndex:r}))),ds(e,c)&&f(d,u-1,t.loop,c),o=a,c=e}d<u-1&&f(d,u-1,t.loop,c)}return h}(t,e,i,n):e}(t,!0===n?[{start:r,end:a,loop:o}]:function(t,e,i,n){const s=t.length,o=[];let r,a=e,l=t[e];for(r=e+1;r<=i;++r){const i=t[r%s];i.skip||i.stop?l.skip||(n=!1,o.push({start:e%s,end:(r-1)%s,loop:n}),e=a=i.stop?r:null):(a=r,l.skip&&(e=r)),l=i}return null!==a&&o.push({start:e%s,end:a%s,loop:n}),o}(i,r,a<r?a+s:a,!!t._fullLoop&&0===r&&a===s-1),i,e)}(this,this.options.segment))}first(){const t=this.segments,e=this.points;return t.length&&e[t[0].start]}last(){const t=this.segments,e=this.points,i=t.length;return i&&e[t[i-1].end]}interpolate(t,e){const i=this.options,n=t[e],s=this.points,o=hs(this,{property:e,start:n,end:n});if(!o.length)return;const r=[],a=function(t){return t.stepped?es:t.tension||"monotone"===t.cubicInterpolationMode?is:ts}(i);let l,h;for(l=0,h=o.length;l<h;++l){const{start:h,end:c}=o[l],d=s[h],u=s[c];if(d===u){r.push(d);continue}const f=a(d,u,Math.abs((n-d[e])/(u[e]-d[e])),i.stepped);f[e]=t[e],r.push(f)}return 1===r.length?r[0]:r}pathSegment(t,e,i){return wr(this)(t,this,e,i)}path(t,e,i){const n=this.segments,s=wr(this);let o=this._loop;e=e||0,i=i||this.points.length-e;for(const r of n)o&=s(t,this,r,{start:e,end:e+i-1});return!!o}draw(t,e,i,n){const s=this.options||{};(this.points||[]).length&&s.borderWidth&&(t.save(),function(t,e,i,n){Mr&&!e.options.segment?function(t,e,i,n){let s=e._path;s||(s=e._path=new Path2D,e.path(s,i,n)&&s.closePath()),br(t,e.options),t.stroke(s)}(t,e,i,n):function(t,e,i,n){const{segments:s,options:o}=e,r=wr(e);for(const a of s)br(t,o,a.style),t.beginPath(),r(t,e,a,{start:i,end:i+n-1})&&t.closePath(),t.stroke()}(t,e,i,n)}(t,this,i,n),t.restore()),this.animated&&(this._pointsUpdated=!1,this._path=void 0)}}function Sr(t,e,i,n){const s=t.options,{[i]:o}=t.getProps([i],n);return Math.abs(e-o)<s.radius+s.hitRadius}function Or(t,e){const{x:i,y:n,base:s,width:o,height:r}=t.getProps(["x","y","base","width","height"],e);let a,l,h,c,d;return t.horizontal?(d=r/2,a=Math.min(i,s),l=Math.max(i,s),h=n-d,c=n+d):(d=o/2,a=i-d,l=i+d,h=Math.min(n,s),c=Math.max(n,s)),{left:a,top:h,right:l,bottom:c}}function Dr(t,e,i,n){return t?0:bi(e,i,n)}function Pr(t,e,i,n){const s=null===e,o=null===i,r=t&&!(s&&o)&&Or(t,n);return r&&(s||xi(e,r.left,r.right))&&(o||xi(i,r.top,r.bottom))}function Ar(t,e){t.rect(e.x,e.y,e.w,e.h)}function Cr(t,e,i={}){const n=t.x!==i.x?-e:0,s=t.y!==i.y?-e:0,o=(t.x+t.w!==i.x+i.w?e:0)-n,r=(t.y+t.h!==i.y+i.h?e:0)-s;return{x:t.x+n,y:t.y+s,w:t.w+o,h:t.h+r,radius:t.radius}}var Tr=Object.freeze({__proto__:null,ArcElement:class extends Po{static id="arc";static defaults={borderAlign:"center",borderColor:"#fff",borderDash:[],borderDashOffset:0,borderJoinStyle:void 0,borderRadius:0,borderWidth:2,offset:0,spacing:0,angle:void 0,circular:!0,selfJoin:!1};static defaultRoutes={backgroundColor:"backgroundColor"};static descriptors={_scriptable:!0,_indexable:t=>"borderDash"!==t};circumference;endAngle;fullCircles;innerRadius;outerRadius;pixelMargin;startAngle;constructor(t){super(),this.options=void 0,this.circumference=void 0,this.startAngle=void 0,this.endAngle=void 0,this.innerRadius=void 0,this.outerRadius=void 0,this.pixelMargin=0,this.fullCircles=0,t&&Object.assign(this,t)}inRange(t,e,i){const n=this.getProps(["x","y"],i),{angle:s,distance:o}=ui(n,{x:t,y:e}),{startAngle:r,endAngle:a,innerRadius:l,outerRadius:h,circumference:c}=this.getProps(["startAngle","endAngle","innerRadius","outerRadius","circumference"],i),d=(this.options.spacing+this.options.borderWidth)/2,u=Ee(c,a-r),f=mi(s,r,a)&&r!==a,p=u>=Ge||f,g=xi(o,l+d,h+d);return p&&g}getCenterPoint(t){const{x:e,y:i,startAngle:n,endAngle:s,innerRadius:o,outerRadius:r}=this.getProps(["x","y","startAngle","endAngle","innerRadius","outerRadius"],t),{offset:a,spacing:l}=this.options,h=(n+s)/2,c=(o+r+l+a)/2;return{x:e+Math.cos(h)*c,y:i+Math.sin(h)*c}}tooltipPosition(t){return this.getCenterPoint(t)}draw(t){const{options:e,circumference:i}=this,n=(e.offset||0)/4,s=(e.spacing||0)/2,o=e.circular;if(this.pixelMargin="inner"===e.borderAlign?.33:0,this.fullCircles=i>Ge?Math.floor(i/Ge):0,0===i||this.innerRadius<0||this.outerRadius<0)return;t.save();const r=(this.startAngle+this.endAngle)/2;t.translate(Math.cos(r)*n,Math.sin(r)*n);const a=n*(1-Math.sin(Math.min(Ke,i||0)));t.fillStyle=e.backgroundColor,t.strokeStyle=e.borderColor,function(t,e,i,n,s){const{fullCircles:o,startAngle:r,circumference:a}=e;let l=e.endAngle;if(o){mr(t,e,i,n,l,s);for(let e=0;e<o;++e)t.fill();isNaN(a)||(l=r+(a%Ge||Ge))}mr(t,e,i,n,l,s),t.fill()}(t,this,a,s,o),function(t,e,i,n,s){const{fullCircles:o,startAngle:r,circumference:a,options:l}=e,{borderWidth:h,borderJoinStyle:c,borderDash:d,borderDashOffset:u,borderRadius:f}=l,p="inner"===l.borderAlign;if(!h)return;t.setLineDash(d||[]),t.lineDashOffset=u,p?(t.lineWidth=2*h,t.lineJoin=c||"round"):(t.lineWidth=h,t.lineJoin=c||"bevel");let g=e.endAngle;if(o){mr(t,e,i,n,g,s);for(let e=0;e<o;++e)t.stroke();isNaN(a)||(g=r+(a%Ge||Ge))}p&&function(t,e,i){const{startAngle:n,pixelMargin:s,x:o,y:r,outerRadius:a,innerRadius:l}=e;let h=s/a;t.beginPath(),t.arc(o,r,a,n-h,i+h),l>s?(h=s/l,t.arc(o,r,l,i+h,n-h,!0)):t.arc(o,r,s,i+ti,n-ti),t.closePath(),t.clip()}(t,e,g),l.selfJoin&&g-r>=Ke&&0===f&&"miter"!==c&&function(t,e,i){const{startAngle:n,x:s,y:o,outerRadius:r,innerRadius:a,options:l}=e,{borderWidth:h,borderJoinStyle:c}=l,d=Math.min(h/r,gi(n-i));if(t.beginPath(),t.arc(s,o,r-h/2,n+d/2,i-d/2),a>0){const e=Math.min(h/a,gi(n-i));t.arc(s,o,a+h/2,i-e/2,n+e/2,!0)}else{const e=Math.min(h/2,r*gi(n-i));if("round"===c)t.arc(s,o,e,i-Ke/2,n+Ke/2,!0);else if("bevel"===c){const r=2*e*e,a=-r*Math.cos(i+Ke/2)+s,l=-r*Math.sin(i+Ke/2)+o,h=r*Math.cos(n+Ke/2)+s,c=r*Math.sin(n+Ke/2)+o;t.lineTo(a,l),t.lineTo(h,c)}}t.closePath(),t.moveTo(0,0),t.rect(0,0,t.canvas.width,t.canvas.height),t.clip("evenodd")}(t,e,g),o||(mr(t,e,i,n,g,s),t.stroke())}(t,this,a,s,o),t.restore()}},BarElement:class extends Po{static id="bar";static defaults={borderSkipped:"start",borderWidth:0,borderRadius:0,inflateAmount:"auto",pointStyle:void 0};static defaultRoutes={backgroundColor:"backgroundColor",borderColor:"borderColor"};constructor(t){super(),this.options=void 0,this.horizontal=void 0,this.base=void 0,this.width=void 0,this.height=void 0,this.inflateAmount=void 0,t&&Object.assign(this,t)}draw(t){const{inflateAmount:e,options:{borderColor:i,backgroundColor:n}}=this,{inner:s,outer:o}=function(t){const e=Or(t),i=e.right-e.left,n=e.bottom-e.top,s=function(t,e,i){const n=t.options.borderWidth,s=t.borderSkipped,o=bn(n);return{t:Dr(s.top,o.top,0,i),r:Dr(s.right,o.right,0,e),b:Dr(s.bottom,o.bottom,0,i),l:Dr(s.left,o.left,0,e)}}(t,i/2,n/2),o=function(t,e,i){const{enableBorderRadius:n}=t.getProps(["enableBorderRadius"]),s=t.options.borderRadius,o=xn(s),r=Math.min(e,i),a=t.borderSkipped,l=n||Ae(s);return{topLeft:Dr(!l||a.top||a.left,o.topLeft,0,r),topRight:Dr(!l||a.top||a.right,o.topRight,0,r),bottomLeft:Dr(!l||a.bottom||a.left,o.bottomLeft,0,r),bottomRight:Dr(!l||a.bottom||a.right,o.bottomRight,0,r)}}(t,i/2,n/2);return{outer:{x:e.left,y:e.top,w:i,h:n,radius:o},inner:{x:e.left+s.l,y:e.top+s.t,w:i-s.l-s.r,h:n-s.t-s.b,radius:{topLeft:Math.max(0,o.topLeft-Math.max(s.t,s.l)),topRight:Math.max(0,o.topRight-Math.max(s.t,s.r)),bottomLeft:Math.max(0,o.bottomLeft-Math.max(s.b,s.l)),bottomRight:Math.max(0,o.bottomRight-Math.max(s.b,s.r))}}}}(this),r=(a=o.radius).topLeft||a.topRight||a.bottomLeft||a.bottomRight?dn:Ar;var a;t.save(),o.w===s.w&&o.h===s.h||(t.beginPath(),r(t,Cr(o,e,s)),t.clip(),r(t,Cr(s,-e,o)),t.fillStyle=i,t.fill("evenodd")),t.beginPath(),r(t,Cr(s,e)),t.fillStyle=n,t.fill(),t.restore()}inRange(t,e,i){return Pr(this,t,e,i)}inXRange(t,e){return Pr(this,t,null,e)}inYRange(t,e){return Pr(this,null,t,e)}getCenterPoint(t){const{x:e,y:i,base:n,horizontal:s}=this.getProps(["x","y","base","horizontal"],t);return{x:s?(e+n)/2:e,y:s?i:(i+n)/2}}getRange(t){return"x"===t?this.width/2:this.height/2}},LineElement:kr,PointElement:class extends Po{static id="point";parsed;skip;stop;static defaults={borderWidth:1,hitRadius:1,hoverBorderWidth:1,hoverRadius:4,pointStyle:"circle",radius:3,rotation:0};static defaultRoutes={backgroundColor:"backgroundColor",borderColor:"borderColor"};constructor(t){super(),this.options=void 0,this.parsed=void 0,this.skip=void 0,this.stop=void 0,t&&Object.assign(this,t)}inRange(t,e,i){const n=this.options,{x:s,y:o}=this.getProps(["x","y"],i);return Math.pow(t-s,2)+Math.pow(e-o,2)<Math.pow(n.hitRadius+n.radius,2)}inXRange(t,e){return Sr(this,t,"x",e)}inYRange(t,e){return Sr(this,t,"y",e)}getCenterPoint(t){const{x:e,y:i}=this.getProps(["x","y"],t);return{x:e,y:i}}size(t){let e=(t=t||this.options||{}).radius||0;return e=Math.max(e,e&&t.hoverRadius||0),2*(e+(e&&t.borderWidth||0))}draw(t,e){const i=this.options;this.skip||i.radius<.1||!nn(this,e,this.size(i)/2)||(t.strokeStyle=i.borderColor,t.lineWidth=i.borderWidth,t.fillStyle=i.backgroundColor,tn(t,i,this.x,this.y))}getRange(){const t=this.options||{};return t.radius+t.hitRadius}}});const Er=["rgb(54, 162, 235)","rgb(255, 99, 132)","rgb(255, 159, 64)","rgb(255, 205, 86)","rgb(75, 192, 192)","rgb(153, 102, 255)","rgb(201, 203, 207)"],Lr=Er.map(t=>t.replace("rgb(","rgba(").replace(")",", 0.5)"));function Rr(t){return Er[t%Er.length]}function Ir(t){return Lr[t%Lr.length]}function Fr(t){let e;for(e in t)if(t[e].borderColor||t[e].backgroundColor)return!0;return!1}var zr={id:"colors",defaults:{enabled:!0,forceOverride:!1},beforeLayout(t,e,i){if(!i.enabled)return;const{data:{datasets:n},options:s}=t.config,{elements:o}=s,r=Fr(n)||(a=s)&&(a.borderColor||a.backgroundColor)||o&&Fr(o)||"rgba(0,0,0,0.1)"!==Ki.borderColor||"rgba(0,0,0,0.1)"!==Ki.backgroundColor;var a;if(!i.forceOverride&&r)return;const l=function(t){let e=0;return(i,n)=>{const s=t.getDatasetMeta(n).controller;s instanceof Ws?e=function(t,e){return t.backgroundColor=t.data.map(()=>Rr(e++)),e}(i,e):s instanceof js?e=function(t,e){return t.backgroundColor=t.data.map(()=>Ir(e++)),e}(i,e):s&&(e=function(t,e){return t.borderColor=Rr(e),t.backgroundColor=Ir(e),++e}(i,e))}}(t);n.forEach(l)}};function Vr(t){if(t._decimated){const e=t._data;delete t._decimated,delete t._data,Object.defineProperty(t,"data",{configurable:!0,enumerable:!0,writable:!0,value:e})}}function Br(t){t.data.datasets.forEach(t=>{Vr(t)})}var Wr={id:"decimation",defaults:{algorithm:"min-max",enabled:!1},beforeElementsUpdate:(t,e,i)=>{if(!i.enabled)return void Br(t);const n=t.width;t.data.datasets.forEach((e,s)=>{const{_data:o,indexAxis:r}=e,a=t.getDatasetMeta(s),l=o||e.data;if("y"===_n([r,t.options.indexAxis]))return;if(!a.controller.supportsDecimation)return;const h=t.scales[a.xAxisID];if("linear"!==h.type&&"time"!==h.type)return;if(t.options.parsing)return;let c,{start:d,count:u}=function(t,e){const i=e.length;let n,s=0;const{iScale:o}=t,{min:r,max:a,minDefined:l,maxDefined:h}=o.getUserBounds();return l&&(s=bi(vi(e,o.axis,r).lo,0,i-1)),n=h?bi(vi(e,o.axis,a).hi+1,s,i)-s:i-s,{start:s,count:n}}(a,l);if(u<=(i.threshold||4*n))Vr(e);else{switch(De(o)&&(e._data=l,delete e.data,Object.defineProperty(e,"data",{configurable:!0,enumerable:!0,get:function(){return this._decimated},set:function(t){this._data=t}})),i.algorithm){case"lttb":c=function(t,e,i,n,s){const o=s.samples||n;if(o>=i)return t.slice(e,e+i);const r=[],a=(i-2)/(o-2);let l=0;const h=e+i-1;let c,d,u,f,p,g=e;for(r[l++]=t[g],c=0;c<o-2;c++){let n,s=0,o=0;const h=Math.floor((c+1)*a)+1+e,m=Math.min(Math.floor((c+2)*a)+1,i)+e,b=m-h;for(n=h;n<m;n++)s+=t[n].x,o+=t[n].y;s/=b,o/=b;const x=Math.floor(c*a)+1+e,y=Math.min(Math.floor((c+1)*a)+1,i)+e,{x:v,y:_}=t[g];for(u=f=-1,n=x;n<y;n++)f=.5*Math.abs((v-s)*(t[n].y-_)-(v-t[n].x)*(o-_)),f>u&&(u=f,d=t[n],p=n);r[l++]=d,g=p}return r[l++]=t[h],r}(l,d,u,n,i);break;case"min-max":c=function(t,e,i,n){let s,o,r,a,l,h,c,d,u,f,p=0,g=0;const m=[],b=e+i-1,x=t[e].x,y=t[b].x-x;for(s=e;s<e+i;++s){o=t[s],r=(o.x-x)/y*n,a=o.y;const e=0|r;if(e===l)a<u?(u=a,h=s):a>f&&(f=a,c=s),p=(g*p+o.x)/++g;else{const i=s-1;if(!De(h)&&!De(c)){const e=Math.min(h,c),n=Math.max(h,c);e!==d&&e!==i&&m.push({...t[e],x:p}),n!==d&&n!==i&&m.push({...t[n],x:p})}s>0&&i!==d&&m.push(t[i]),m.push(o),l=e,g=0,u=f=a,h=c=d=s}}return m}(l,d,u,n);break;default:throw new Error(`Unsupported decimation algorithm '${i.algorithm}'`)}e._decimated=c}})},destroy(t){Br(t)}};function jr(t,e,i,n){if(n)return;let s=e[t],o=i[t];return"angle"===t&&(s=gi(s),o=gi(o)),{property:t,start:s,end:o}}function Nr(t,e,i){for(;e>t;e--){const t=i[e];if(!isNaN(t.x)&&!isNaN(t.y))break}return e}function Hr(t,e,i,n){return t&&e?n(t[i],e[i]):t?t[i]:e?e[i]:0}function $r(t,e){let i=[],n=!1;return Pe(t)?(n=!0,i=t):i=function(t,e){const{x:i=null,y:n=null}=t||{},s=e.points,o=[];return e.segments.forEach(({start:t,end:e})=>{e=Nr(t,e,s);const r=s[t],a=s[e];null!==n?(o.push({x:r.x,y:n}),o.push({x:a.x,y:n})):null!==i&&(o.push({x:i,y:r.y}),o.push({x:i,y:a.y}))}),o}(t,e),i.length?new kr({points:i,options:{tension:0},_loop:n,_fullLoop:n}):null}function Ur(t){return t&&!1!==t.fill}function Yr(t,e,i){let n=t[e].fill;const s=[e];let o;if(!i)return n;for(;!1!==n&&-1===s.indexOf(n);){if(!Ce(n))return n;if(o=t[n],!o)return!1;if(o.visible)return n;s.push(n),n=o.fill}return!1}function Xr(t,e,i){const n=function(t){const e=t.options,i=e.fill;let n=Ee(i&&i.target,i);return void 0===n&&(n=!!e.backgroundColor),!1!==n&&null!==n&&(!0===n?"origin":n)}(t);if(Ae(n))return!isNaN(n.value)&&n;let s=parseFloat(n);return Ce(s)&&Math.floor(s)===s?function(t,e,i,n){return"-"!==t&&"+"!==t||(i=e+i),!(i===e||i<0||i>=n)&&i}(n[0],e,s,i):["origin","start","end","stack","shape"].indexOf(n)>=0&&n}function qr(t,e,i){const n=[];for(let s=0;s<i.length;s++){const o=i[s],{first:r,last:a,point:l}=Kr(o,e,"x");if(!(!l||r&&a))if(r)n.unshift(l);else if(t.push(l),!a)break}t.push(...n)}function Kr(t,e,i){const n=t.interpolate(e,i);if(!n)return{};const s=n[i],o=t.segments,r=t.points;let a=!1,l=!1;for(let t=0;t<o.length;t++){const e=o[t],n=r[e.start][i],h=r[e.end][i];if(xi(s,n,h)){a=s===n,l=s===h;break}}return{first:a,last:l,point:n}}class Gr{constructor(t){this.x=t.x,this.y=t.y,this.radius=t.radius}pathSegment(t,e,i){const{x:n,y:s,radius:o}=this;return e=e||{start:0,end:Ge},t.arc(n,s,o,e.end,e.start,!0),!i.bounds}interpolate(t){const{x:e,y:i,radius:n}=this,s=t.angle;return{x:e+Math.cos(s)*n,y:i+Math.sin(s)*n,angle:s}}}function Jr(t,e,i){const n=function(t){const{chart:e,fill:i,line:n}=t;if(Ce(i))return function(t,e){const i=t.getDatasetMeta(e);return i&&t.isDatasetVisible(e)?i.dataset:null}(e,i);if("stack"===i)return function(t){const{scale:e,index:i,line:n}=t,s=[],o=n.segments,r=n.points,a=function(t,e){const i=[],n=t.getMatchingVisibleMetas("line");for(let t=0;t<n.length;t++){const s=n[t];if(s.index===e)break;s.hidden||i.unshift(s.dataset)}return i}(e,i);a.push($r({x:null,y:e.bottom},n));for(let t=0;t<o.length;t++){const e=o[t];for(let t=e.start;t<=e.end;t++)qr(s,r[t],a)}return new kr({points:s,options:{}})}(t);if("shape"===i)return!0;const s=function(t){return(t.scale||{}).getPointPositionForValue?function(t){const{scale:e,fill:i}=t,n=e.options,s=e.getLabels().length,o=n.reverse?e.max:e.min,r=function(t,e,i){let n;return n="start"===t?i:"end"===t?e.options.reverse?e.min:e.max:Ae(t)?t.value:e.getBaseValue(),n}(i,e,o),a=[];if(n.grid.circular){const t=e.getPointPositionForValue(0,o);return new Gr({x:t.x,y:t.y,radius:e.getDistanceFromCenterForValue(r)})}for(let t=0;t<s;++t)a.push(e.getPointPositionForValue(t,r));return a}(t):function(t){const{scale:e={},fill:i}=t,n=function(t,e){let i=null;return"start"===t?i=e.bottom:"end"===t?i=e.top:Ae(t)?i=e.getPixelForValue(t.value):e.getBasePixel&&(i=e.getBasePixel()),i}(i,e);if(Ce(n)){const t=e.isHorizontal();return{x:t?n:null,y:t?null:n}}return null}(t)}(t);return s instanceof Gr?s:$r(s,n)}(e),{chart:s,index:o,line:r,scale:a,axis:l}=e,h=r.options,c=h.fill,d=h.backgroundColor,{above:u=d,below:f=d}=c||{},p=s.getDatasetMeta(o),g=fs(s,p);n&&r.points.length&&(sn(t,i),function(t,e){const{line:i,target:n,above:s,below:o,area:r,scale:a,clip:l}=e,h=i._loop?"angle":e.axis;t.save();let c=o;o!==s&&("x"===h?(Zr(t,n,r.top),ta(t,{line:i,target:n,color:s,scale:a,property:h,clip:l}),t.restore(),t.save(),Zr(t,n,r.bottom)):"y"===h&&(Qr(t,n,r.left),ta(t,{line:i,target:n,color:o,scale:a,property:h,clip:l}),t.restore(),t.save(),Qr(t,n,r.right),c=s)),ta(t,{line:i,target:n,color:c,scale:a,property:h,clip:l}),t.restore()}(t,{line:r,target:n,above:u,below:f,area:i,scale:a,axis:l,clip:g}),on(t))}function Zr(t,e,i){const{segments:n,points:s}=e;let o=!0,r=!1;t.beginPath();for(const a of n){const{start:n,end:l}=a,h=s[n],c=s[Nr(n,l,s)];o?(t.moveTo(h.x,h.y),o=!1):(t.lineTo(h.x,i),t.lineTo(h.x,h.y)),r=!!e.pathSegment(t,a,{move:r}),r?t.closePath():t.lineTo(c.x,i)}t.lineTo(e.first().x,i),t.closePath(),t.clip()}function Qr(t,e,i){const{segments:n,points:s}=e;let o=!0,r=!1;t.beginPath();for(const a of n){const{start:n,end:l}=a,h=s[n],c=s[Nr(n,l,s)];o?(t.moveTo(h.x,h.y),o=!1):(t.lineTo(i,h.y),t.lineTo(h.x,h.y)),r=!!e.pathSegment(t,a,{move:r}),r?t.closePath():t.lineTo(i,c.y)}t.lineTo(i,e.first().y),t.closePath(),t.clip()}function ta(t,e){const{line:i,target:n,property:s,color:o,scale:r,clip:a}=e,l=function(t,e,i){const n=t.segments,s=t.points,o=e.points,r=[];for(const t of n){let{start:n,end:a}=t;a=Nr(n,a,s);const l=jr(i,s[n],s[a],t.loop);if(!e.segments){r.push({source:t,target:l,start:s[n],end:s[a]});continue}const h=hs(e,l);for(const e of h){const n=jr(i,o[e.start],o[e.end],e.loop),a=ls(t,s,n);for(const t of a)r.push({source:t,target:e,start:{[i]:Hr(l,n,"start",Math.max)},end:{[i]:Hr(l,n,"end",Math.min)}})}}return r}(i,n,s);for(const{source:e,target:h,start:c,end:d}of l){const{style:{backgroundColor:l=o}={}}=e,u=!0!==n;t.save(),t.fillStyle=l,ea(t,r,a,u&&jr(s,c,d)),t.beginPath();const f=!!i.pathSegment(t,e);let p;if(u){f?t.closePath():ia(t,n,d,s);const e=!!n.pathSegment(t,h,{move:f,reverse:!0});p=f&&e,p||ia(t,n,c,s)}t.closePath(),t.fill(p?"evenodd":"nonzero"),t.restore()}}function ea(t,e,i,n){const s=e.chart.chartArea,{property:o,start:r,end:a}=n||{};if("x"===o||"y"===o){let e,n,l,h;"x"===o?(e=r,n=s.top,l=a,h=s.bottom):(e=s.left,n=r,l=s.right,h=a),t.beginPath(),i&&(e=Math.max(e,i.left),l=Math.min(l,i.right),n=Math.max(n,i.top),h=Math.min(h,i.bottom)),t.rect(e,n,l-e,h-n),t.clip()}}function ia(t,e,i,n){const s=e.interpolate(i,n);s&&t.lineTo(s.x,s.y)}var na={id:"filler",afterDatasetsUpdate(t,e,i){const n=(t.data.datasets||[]).length,s=[];let o,r,a,l;for(r=0;r<n;++r)o=t.getDatasetMeta(r),a=o.dataset,l=null,a&&a.options&&a instanceof kr&&(l={visible:t.isDatasetVisible(r),index:r,fill:Xr(a,r,n),chart:t,axis:o.controller.options.indexAxis,scale:o.vScale,line:a}),o.$filler=l,s.push(l);for(r=0;r<n;++r)l=s[r],l&&!1!==l.fill&&(l.fill=Yr(s,r,i.propagate))},beforeDraw(t,e,i){const n="beforeDraw"===i.drawTime,s=t.getSortedVisibleDatasetMetas(),o=t.chartArea;for(let e=s.length-1;e>=0;--e){const i=s[e].$filler;i&&(i.line.updateControlPoints(o,i.axis),n&&i.fill&&Jr(t.ctx,i,o))}},beforeDatasetsDraw(t,e,i){if("beforeDatasetsDraw"!==i.drawTime)return;const n=t.getSortedVisibleDatasetMetas();for(let e=n.length-1;e>=0;--e){const i=n[e].$filler;Ur(i)&&Jr(t.ctx,i,t.chartArea)}},beforeDatasetDraw(t,e,i){const n=e.meta.$filler;Ur(n)&&"beforeDatasetDraw"===i.drawTime&&Jr(t.ctx,n,t.chartArea)},defaults:{propagate:!0,drawTime:"beforeDatasetDraw"}};const sa=(t,e)=>{let{boxHeight:i=e,boxWidth:n=e}=t;return t.usePointStyle&&(i=Math.min(i,e),n=t.pointStyleWidth||Math.min(n,e)),{boxWidth:n,boxHeight:i,itemHeight:Math.max(e,i)}};class oa extends Po{constructor(t){super(),this._added=!1,this.legendHitBoxes=[],this._hoveredItem=null,this.doughnutMode=!1,this.chart=t.chart,this.options=t.options,this.ctx=t.ctx,this.legendItems=void 0,this.columnSizes=void 0,this.lineWidths=void 0,this.maxHeight=void 0,this.maxWidth=void 0,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.height=void 0,this.width=void 0,this._margins=void 0,this.position=void 0,this.weight=void 0,this.fullSize=void 0}update(t,e,i){this.maxWidth=t,this.maxHeight=e,this._margins=i,this.setDimensions(),this.buildLabels(),this.fit()}setDimensions(){this.isHorizontal()?(this.width=this.maxWidth,this.left=this._margins.left,this.right=this.width):(this.height=this.maxHeight,this.top=this._margins.top,this.bottom=this.height)}buildLabels(){const t=this.options.labels||{};let e=Re(t.generateLabels,[this.chart],this)||[];t.filter&&(e=e.filter(e=>t.filter(e,this.chart.data))),t.sort&&(e=e.sort((e,i)=>t.sort(e,i,this.chart.data))),this.options.reverse&&e.reverse(),this.legendItems=e}fit(){const{options:t,ctx:e}=this;if(!t.display)return void(this.width=this.height=0);const i=t.labels,n=vn(i.font),s=n.size,o=this._computeTitleHeight(),{boxWidth:r,itemHeight:a}=sa(i,s);let l,h;e.font=n.string,this.isHorizontal()?(l=this.maxWidth,h=this._fitRows(o,s,r,a)+10):(h=this.maxHeight,l=this._fitCols(o,n,r,a)+10),this.width=Math.min(l,t.maxWidth||this.maxWidth),this.height=Math.min(h,t.maxHeight||this.maxHeight)}_fitRows(t,e,i,n){const{ctx:s,maxWidth:o,options:{labels:{padding:r}}}=this,a=this.legendHitBoxes=[],l=this.lineWidths=[0],h=n+r;let c=t;s.textAlign="left",s.textBaseline="middle";let d=-1,u=-h;return this.legendItems.forEach((t,f)=>{const p=i+e/2+s.measureText(t.text).width;(0===f||l[l.length-1]+p+2*r>o)&&(c+=h,l[l.length-(f>0?0:1)]=0,u+=h,d++),a[f]={left:0,top:u,row:d,width:p,height:n},l[l.length-1]+=p+r}),c}_fitCols(t,e,i,n){const{ctx:s,maxHeight:o,options:{labels:{padding:r}}}=this,a=this.legendHitBoxes=[],l=this.columnSizes=[],h=o-t;let c=r,d=0,u=0,f=0,p=0;return this.legendItems.forEach((t,o)=>{const{itemWidth:g,itemHeight:m}=function(t,e,i,n,s){const o=function(t,e,i,n){let s=t.text;return s&&"string"!=typeof s&&(s=s.reduce((t,e)=>t.length>e.length?t:e)),e+i.size/2+n.measureText(s).width}(n,t,e,i),r=function(t,e,i){let n=t;return"string"!=typeof e.text&&(n=ra(e,i)),n}(s,n,e.lineHeight);return{itemWidth:o,itemHeight:r}}(i,e,s,t,n);o>0&&u+m+2*r>h&&(c+=d+r,l.push({width:d,height:u}),f+=d+r,p++,d=u=0),a[o]={left:f,top:u,col:p,width:g,height:m},d=Math.max(d,g),u+=m+r}),c+=d,l.push({width:d,height:u}),c}adjustHitBoxes(){if(!this.options.display)return;const t=this._computeTitleHeight(),{legendHitBoxes:e,options:{align:i,labels:{padding:n},rtl:s}}=this,o=ns(s,this.left,this.width);if(this.isHorizontal()){let s=0,r=Pi(i,this.left+n,this.right-this.lineWidths[s]);for(const a of e)s!==a.row&&(s=a.row,r=Pi(i,this.left+n,this.right-this.lineWidths[s])),a.top+=this.top+t+n,a.left=o.leftForLtr(o.x(r),a.width),r+=a.width+n}else{let s=0,r=Pi(i,this.top+t+n,this.bottom-this.columnSizes[s].height);for(const a of e)a.col!==s&&(s=a.col,r=Pi(i,this.top+t+n,this.bottom-this.columnSizes[s].height)),a.top=r,a.left+=this.left+n,a.left=o.leftForLtr(o.x(a.left),a.width),r+=a.height+n}}isHorizontal(){return"top"===this.options.position||"bottom"===this.options.position}draw(){if(this.options.display){const t=this.ctx;sn(t,this),this._draw(),on(t)}}_draw(){const{options:t,columnSizes:e,lineWidths:i,ctx:n}=this,{align:s,labels:o}=t,r=Ki.color,a=ns(t.rtl,this.left,this.width),l=vn(o.font),{padding:h}=o,c=l.size,d=c/2;let u;this.drawTitle(),n.textAlign=a.textAlign("left"),n.textBaseline="middle",n.lineWidth=.5,n.font=l.string;const{boxWidth:f,boxHeight:p,itemHeight:g}=sa(o,c),m=this.isHorizontal(),b=this._computeTitleHeight();u=m?{x:Pi(s,this.left+h,this.right-i[0]),y:this.top+h+b,line:0}:{x:this.left+h,y:Pi(s,this.top+b+h,this.bottom-e[0].height),line:0},ss(this.ctx,t.textDirection);const x=g+h;this.legendItems.forEach((y,v)=>{n.strokeStyle=y.fontColor,n.fillStyle=y.fontColor;const _=n.measureText(y.text).width,w=a.textAlign(y.textAlign||(y.textAlign=o.textAlign)),M=f+d+_;let k=u.x,S=u.y;if(a.setWidth(this.width),m?v>0&&k+M+h>this.right&&(S=u.y+=x,u.line++,k=u.x=Pi(s,this.left+h,this.right-i[u.line])):v>0&&S+x>this.bottom&&(k=u.x=k+e[u.line].width+h,u.line++,S=u.y=Pi(s,this.top+b+h,this.bottom-e[u.line].height)),function(t,e,i){if(isNaN(f)||f<=0||isNaN(p)||p<0)return;n.save();const s=Ee(i.lineWidth,1);if(n.fillStyle=Ee(i.fillStyle,r),n.lineCap=Ee(i.lineCap,"butt"),n.lineDashOffset=Ee(i.lineDashOffset,0),n.lineJoin=Ee(i.lineJoin,"miter"),n.lineWidth=s,n.strokeStyle=Ee(i.strokeStyle,r),n.setLineDash(Ee(i.lineDash,[])),o.usePointStyle){const r={radius:p*Math.SQRT2/2,pointStyle:i.pointStyle,rotation:i.rotation,borderWidth:s},l=a.xPlus(t,f/2);en(n,r,l,e+d,o.pointStyleWidth&&f)}else{const o=e+Math.max((c-p)/2,0),r=a.leftForLtr(t,f),l=xn(i.borderRadius);n.beginPath(),Object.values(l).some(t=>0!==t)?dn(n,{x:r,y:o,w:f,h:p,radius:l}):n.rect(r,o,f,p),n.fill(),0!==s&&n.stroke()}n.restore()}(a.x(k),S,y),k=((t,e,i,n)=>t===(n?"left":"right")?i:"center"===t?(e+i)/2:e)(w,k+f+d,m?k+M:this.right,t.rtl),function(t,e,i){cn(n,i.text,t,e+g/2,l,{strikethrough:i.hidden,textAlign:a.textAlign(i.textAlign)})}(a.x(k),S,y),m)u.x+=M+h;else if("string"!=typeof y.text){const t=l.lineHeight;u.y+=ra(y,t)+h}else u.y+=x}),os(this.ctx,t.textDirection)}drawTitle(){const t=this.options,e=t.title,i=vn(e.font),n=yn(e.padding);if(!e.display)return;const s=ns(t.rtl,this.left,this.width),o=this.ctx,r=e.position,a=i.size/2,l=n.top+a;let h,c=this.left,d=this.width;if(this.isHorizontal())d=Math.max(...this.lineWidths),h=this.top+l,c=Pi(t.align,c,this.right-d);else{const e=this.columnSizes.reduce((t,e)=>Math.max(t,e.height),0);h=l+Pi(t.align,this.top,this.bottom-e-t.labels.padding-this._computeTitleHeight())}const u=Pi(r,c,c+d);o.textAlign=s.textAlign(Di(r)),o.textBaseline="middle",o.strokeStyle=e.color,o.fillStyle=e.color,o.font=i.string,cn(o,e.text,u,h,i)}_computeTitleHeight(){const t=this.options.title,e=vn(t.font),i=yn(t.padding);return t.display?e.lineHeight+i.height:0}_getLegendItemAt(t,e){let i,n,s;if(xi(t,this.left,this.right)&&xi(e,this.top,this.bottom))for(s=this.legendHitBoxes,i=0;i<s.length;++i)if(n=s[i],xi(t,n.left,n.left+n.width)&&xi(e,n.top,n.top+n.height))return this.legendItems[i];return null}handleEvent(t){const e=this.options;if(!function(t,e){return!("mousemove"!==t&&"mouseout"!==t||!e.onHover&&!e.onLeave)||!(!e.onClick||"click"!==t&&"mouseup"!==t)}(t.type,e))return;const i=this._getLegendItemAt(t.x,t.y);if("mousemove"===t.type||"mouseout"===t.type){const o=this._hoveredItem,r=(s=i,null!==(n=o)&&null!==s&&n.datasetIndex===s.datasetIndex&&n.index===s.index);o&&!r&&Re(e.onLeave,[t,o,this],this),this._hoveredItem=i,i&&!r&&Re(e.onHover,[t,i,this],this)}else i&&Re(e.onClick,[t,i,this],this);var n,s}}function ra(t,e){return e*(t.text?t.text.length:0)}var aa={id:"legend",_element:oa,start(t,e,i){const n=t.legend=new oa({ctx:t.ctx,options:i,chart:t});ho.configure(t,n,i),ho.addBox(t,n)},stop(t){ho.removeBox(t,t.legend),delete t.legend},beforeUpdate(t,e,i){const n=t.legend;ho.configure(t,n,i),n.options=i},afterUpdate(t){const e=t.legend;e.buildLabels(),e.adjustHitBoxes()},afterEvent(t,e){e.replay||t.legend.handleEvent(e.event)},defaults:{display:!0,position:"top",align:"center",fullSize:!0,reverse:!1,weight:1e3,onClick(t,e,i){const n=e.datasetIndex,s=i.chart;s.isDatasetVisible(n)?(s.hide(n),e.hidden=!0):(s.show(n),e.hidden=!1)},onHover:null,onLeave:null,labels:{color:t=>t.chart.options.color,boxWidth:40,padding:10,generateLabels(t){const e=t.data.datasets,{labels:{usePointStyle:i,pointStyle:n,textAlign:s,color:o,useBorderRadius:r,borderRadius:a}}=t.legend.options;return t._getSortedDatasetMetas().map(t=>{const l=t.controller.getStyle(i?0:void 0),h=yn(l.borderWidth);return{text:e[t.index].label,fillStyle:l.backgroundColor,fontColor:o,hidden:!t.visible,lineCap:l.borderCapStyle,lineDash:l.borderDash,lineDashOffset:l.borderDashOffset,lineJoin:l.borderJoinStyle,lineWidth:(h.width+h.height)/4,strokeStyle:l.borderColor,pointStyle:n||l.pointStyle,rotation:l.rotation,textAlign:s||l.textAlign,borderRadius:r&&(a||l.borderRadius),datasetIndex:t.index}},this)}},title:{color:t=>t.chart.options.color,display:!1,position:"center",text:""}},descriptors:{_scriptable:t=>!t.startsWith("on"),labels:{_scriptable:t=>!["generateLabels","filter","sort"].includes(t)}}};class la extends Po{constructor(t){super(),this.chart=t.chart,this.options=t.options,this.ctx=t.ctx,this._padding=void 0,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.width=void 0,this.height=void 0,this.position=void 0,this.weight=void 0,this.fullSize=void 0}update(t,e){const i=this.options;if(this.left=0,this.top=0,!i.display)return void(this.width=this.height=this.right=this.bottom=0);this.width=this.right=t,this.height=this.bottom=e;const n=Pe(i.text)?i.text.length:1;this._padding=yn(i.padding);const s=n*vn(i.font).lineHeight+this._padding.height;this.isHorizontal()?this.height=s:this.width=s}isHorizontal(){const t=this.options.position;return"top"===t||"bottom"===t}_drawArgs(t){const{top:e,left:i,bottom:n,right:s,options:o}=this,r=o.align;let a,l,h,c=0;return this.isHorizontal()?(l=Pi(r,i,s),h=e+t,a=s-i):("left"===o.position?(l=i+t,h=Pi(r,n,e),c=-.5*Ke):(l=s-t,h=Pi(r,e,n),c=.5*Ke),a=n-e),{titleX:l,titleY:h,maxWidth:a,rotation:c}}draw(){const t=this.ctx,e=this.options;if(!e.display)return;const i=vn(e.font),n=i.lineHeight/2+this._padding.top,{titleX:s,titleY:o,maxWidth:r,rotation:a}=this._drawArgs(n);cn(t,e.text,0,0,i,{color:e.color,maxWidth:r,rotation:a,textAlign:Di(e.align),textBaseline:"middle",translation:[s,o]})}}var ha={id:"title",_element:la,start(t,e,i){!function(t,e){const i=new la({ctx:t.ctx,options:e,chart:t});ho.configure(t,i,e),ho.addBox(t,i),t.titleBlock=i}(t,i)},stop(t){const e=t.titleBlock;ho.removeBox(t,e),delete t.titleBlock},beforeUpdate(t,e,i){const n=t.titleBlock;ho.configure(t,n,i),n.options=i},defaults:{align:"center",display:!1,font:{weight:"bold"},fullSize:!0,padding:10,position:"top",text:"",weight:2e3},defaultRoutes:{color:"color"},descriptors:{_scriptable:!0,_indexable:!1}};const ca=new WeakMap;var da={id:"subtitle",start(t,e,i){const n=new la({ctx:t.ctx,options:i,chart:t});ho.configure(t,n,i),ho.addBox(t,n),ca.set(t,n)},stop(t){ho.removeBox(t,ca.get(t)),ca.delete(t)},beforeUpdate(t,e,i){const n=ca.get(t);ho.configure(t,n,i),n.options=i},defaults:{align:"center",display:!1,font:{weight:"normal"},fullSize:!0,padding:0,position:"top",text:"",weight:1500},defaultRoutes:{color:"color"},descriptors:{_scriptable:!0,_indexable:!1}};const ua={average(t){if(!t.length)return!1;let e,i,n=new Set,s=0,o=0;for(e=0,i=t.length;e<i;++e){const i=t[e].element;if(i&&i.hasValue()){const t=i.tooltipPosition();n.add(t.x),s+=t.y,++o}}return 0!==o&&0!==n.size&&{x:[...n].reduce((t,e)=>t+e)/n.size,y:s/o}},nearest(t,e){if(!t.length)return!1;let i,n,s,o=e.x,r=e.y,a=Number.POSITIVE_INFINITY;for(i=0,n=t.length;i<n;++i){const n=t[i].element;if(n&&n.hasValue()){const t=fi(e,n.getCenterPoint());t<a&&(a=t,s=n)}}if(s){const t=s.tooltipPosition();o=t.x,r=t.y}return{x:o,y:r}}};function fa(t,e){return e&&(Pe(e)?Array.prototype.push.apply(t,e):t.push(e)),t}function pa(t){return("string"==typeof t||t instanceof String)&&t.indexOf("\n")>-1?t.split("\n"):t}function ga(t,e){const{element:i,datasetIndex:n,index:s}=e,o=t.getDatasetMeta(n).controller,{label:r,value:a}=o.getLabelAndValue(s);return{chart:t,label:r,parsed:o.getParsed(s),raw:t.data.datasets[n].data[s],formattedValue:a,dataset:o.getDataset(),dataIndex:s,datasetIndex:n,element:i}}function ma(t,e){const i=t.chart.ctx,{body:n,footer:s,title:o}=t,{boxWidth:r,boxHeight:a}=e,l=vn(e.bodyFont),h=vn(e.titleFont),c=vn(e.footerFont),d=o.length,u=s.length,f=n.length,p=yn(e.padding);let g=p.height,m=0,b=n.reduce((t,e)=>t+e.before.length+e.lines.length+e.after.length,0);b+=t.beforeBody.length+t.afterBody.length,d&&(g+=d*h.lineHeight+(d-1)*e.titleSpacing+e.titleMarginBottom),b&&(g+=f*(e.displayColors?Math.max(a,l.lineHeight):l.lineHeight)+(b-f)*l.lineHeight+(b-1)*e.bodySpacing),u&&(g+=e.footerMarginTop+u*c.lineHeight+(u-1)*e.footerSpacing);let x=0;const y=function(t){m=Math.max(m,i.measureText(t).width+x)};return i.save(),i.font=h.string,Ie(t.title,y),i.font=l.string,Ie(t.beforeBody.concat(t.afterBody),y),x=e.displayColors?r+2+e.boxPadding:0,Ie(n,t=>{Ie(t.before,y),Ie(t.lines,y),Ie(t.after,y)}),x=0,i.font=c.string,Ie(t.footer,y),i.restore(),m+=p.width,{width:m,height:g}}function ba(t,e,i,n){const{x:s,width:o}=i,{width:r,chartArea:{left:a,right:l}}=t;let h="center";return"center"===n?h=s<=(a+l)/2?"left":"right":s<=o/2?h="left":s>=r-o/2&&(h="right"),function(t,e,i,n){const{x:s,width:o}=n,r=i.caretSize+i.caretPadding;return"left"===t&&s+o+r>e.width||"right"===t&&s-o-r<0||void 0}(h,t,e,i)&&(h="center"),h}function xa(t,e,i){const n=i.yAlign||e.yAlign||function(t,e){const{y:i,height:n}=e;return i<n/2?"top":i>t.height-n/2?"bottom":"center"}(t,i);return{xAlign:i.xAlign||e.xAlign||ba(t,e,i,n),yAlign:n}}function ya(t,e,i,n){const{caretSize:s,caretPadding:o,cornerRadius:r}=t,{xAlign:a,yAlign:l}=i,h=s+o,{topLeft:c,topRight:d,bottomLeft:u,bottomRight:f}=xn(r);let p=function(t,e){let{x:i,width:n}=t;return"right"===e?i-=n:"center"===e&&(i-=n/2),i}(e,a);const g=function(t,e,i){let{y:n,height:s}=t;return"top"===e?n+=i:n-="bottom"===e?s+i:s/2,n}(e,l,h);return"center"===l?"left"===a?p+=h:"right"===a&&(p-=h):"left"===a?p-=Math.max(c,u)+s:"right"===a&&(p+=Math.max(d,f)+s),{x:bi(p,0,n.width-e.width),y:bi(g,0,n.height-e.height)}}function va(t,e,i){const n=yn(i.padding);return"center"===e?t.x+t.width/2:"right"===e?t.x+t.width-n.right:t.x+n.left}function _a(t){return fa([],pa(t))}function wa(t,e){const i=e&&e.dataset&&e.dataset.tooltip&&e.dataset.tooltip.callbacks;return i?t.override(i):t}const Ma={beforeTitle:Se,title(t){if(t.length>0){const e=t[0],i=e.chart.data.labels,n=i?i.length:0;if(this&&this.options&&"dataset"===this.options.mode)return e.dataset.label||"";if(e.label)return e.label;if(n>0&&e.dataIndex<n)return i[e.dataIndex]}return""},afterTitle:Se,beforeBody:Se,beforeLabel:Se,label(t){if(this&&this.options&&"dataset"===this.options.mode)return t.label+": "+t.formattedValue||t.formattedValue;let e=t.dataset.label||"";e&&(e+=": ");const i=t.formattedValue;return De(i)||(e+=i),e},labelColor(t){const e=t.chart.getDatasetMeta(t.datasetIndex).controller.getStyle(t.dataIndex);return{borderColor:e.borderColor,backgroundColor:e.backgroundColor,borderWidth:e.borderWidth,borderDash:e.borderDash,borderDashOffset:e.borderDashOffset,borderRadius:0}},labelTextColor(){return this.options.bodyColor},labelPointStyle(t){const e=t.chart.getDatasetMeta(t.datasetIndex).controller.getStyle(t.dataIndex);return{pointStyle:e.pointStyle,rotation:e.rotation}},afterLabel:Se,afterBody:Se,beforeFooter:Se,footer:Se,afterFooter:Se};function ka(t,e,i,n){const s=t[e].call(i,n);return void 0===s?Ma[e].call(i,n):s}class Sa extends Po{static positioners=ua;constructor(t){super(),this.opacity=0,this._active=[],this._eventPosition=void 0,this._size=void 0,this._cachedAnimations=void 0,this._tooltipItems=[],this.$animations=void 0,this.$context=void 0,this.chart=t.chart,this.options=t.options,this.dataPoints=void 0,this.title=void 0,this.beforeBody=void 0,this.body=void 0,this.afterBody=void 0,this.footer=void 0,this.xAlign=void 0,this.yAlign=void 0,this.x=void 0,this.y=void 0,this.height=void 0,this.width=void 0,this.caretX=void 0,this.caretY=void 0,this.labelColors=void 0,this.labelPointStyles=void 0,this.labelTextColors=void 0}initialize(t){this.options=t,this._cachedAnimations=void 0,this.$context=void 0}_resolveAnimations(){const t=this._cachedAnimations;if(t)return t;const e=this.chart,i=this.options.setContext(this.getContext()),n=i.enabled&&e.options.animation&&i.animations,s=new ys(this.chart,n);return n._cacheable&&(this._cachedAnimations=Object.freeze(s)),s}getContext(){return this.$context||(this.$context=wn(this.chart.getContext(),{tooltip:this,tooltipItems:this._tooltipItems,type:"tooltip"}))}getTitle(t,e){const{callbacks:i}=e,n=ka(i,"beforeTitle",this,t),s=ka(i,"title",this,t),o=ka(i,"afterTitle",this,t);let r=[];return r=fa(r,pa(n)),r=fa(r,pa(s)),r=fa(r,pa(o)),r}getBeforeBody(t,e){return _a(ka(e.callbacks,"beforeBody",this,t))}getBody(t,e){const{callbacks:i}=e,n=[];return Ie(t,t=>{const e={before:[],lines:[],after:[]},s=wa(i,t);fa(e.before,pa(ka(s,"beforeLabel",this,t))),fa(e.lines,ka(s,"label",this,t)),fa(e.after,pa(ka(s,"afterLabel",this,t))),n.push(e)}),n}getAfterBody(t,e){return _a(ka(e.callbacks,"afterBody",this,t))}getFooter(t,e){const{callbacks:i}=e,n=ka(i,"beforeFooter",this,t),s=ka(i,"footer",this,t),o=ka(i,"afterFooter",this,t);let r=[];return r=fa(r,pa(n)),r=fa(r,pa(s)),r=fa(r,pa(o)),r}_createItems(t){const e=this._active,i=this.chart.data,n=[],s=[],o=[];let r,a,l=[];for(r=0,a=e.length;r<a;++r)l.push(ga(this.chart,e[r]));return t.filter&&(l=l.filter((e,n,s)=>t.filter(e,n,s,i))),t.itemSort&&(l=l.sort((e,n)=>t.itemSort(e,n,i))),Ie(l,e=>{const i=wa(t.callbacks,e);n.push(ka(i,"labelColor",this,e)),s.push(ka(i,"labelPointStyle",this,e)),o.push(ka(i,"labelTextColor",this,e))}),this.labelColors=n,this.labelPointStyles=s,this.labelTextColors=o,this.dataPoints=l,l}update(t,e){const i=this.options.setContext(this.getContext()),n=this._active;let s,o=[];if(n.length){const t=ua[i.position].call(this,n,this._eventPosition);o=this._createItems(i),this.title=this.getTitle(o,i),this.beforeBody=this.getBeforeBody(o,i),this.body=this.getBody(o,i),this.afterBody=this.getAfterBody(o,i),this.footer=this.getFooter(o,i);const e=this._size=ma(this,i),r=Object.assign({},t,e),a=xa(this.chart,i,r),l=ya(i,r,a,this.chart);this.xAlign=a.xAlign,this.yAlign=a.yAlign,s={opacity:1,x:l.x,y:l.y,width:e.width,height:e.height,caretX:t.x,caretY:t.y}}else 0!==this.opacity&&(s={opacity:0});this._tooltipItems=o,this.$context=void 0,s&&this._resolveAnimations().update(this,s),t&&i.external&&i.external.call(this,{chart:this.chart,tooltip:this,replay:e})}drawCaret(t,e,i,n){const s=this.getCaretPosition(t,i,n);e.lineTo(s.x1,s.y1),e.lineTo(s.x2,s.y2),e.lineTo(s.x3,s.y3)}getCaretPosition(t,e,i){const{xAlign:n,yAlign:s}=this,{caretSize:o,cornerRadius:r}=i,{topLeft:a,topRight:l,bottomLeft:h,bottomRight:c}=xn(r),{x:d,y:u}=t,{width:f,height:p}=e;let g,m,b,x,y,v;return"center"===s?(y=u+p/2,"left"===n?(g=d,m=g-o,x=y+o,v=y-o):(g=d+f,m=g+o,x=y-o,v=y+o),b=g):(m="left"===n?d+Math.max(a,h)+o:"right"===n?d+f-Math.max(l,c)-o:this.caretX,"top"===s?(x=u,y=x-o,g=m-o,b=m+o):(x=u+p,y=x+o,g=m+o,b=m-o),v=x),{x1:g,x2:m,x3:b,y1:x,y2:y,y3:v}}drawTitle(t,e,i){const n=this.title,s=n.length;let o,r,a;if(s){const l=ns(i.rtl,this.x,this.width);for(t.x=va(this,i.titleAlign,i),e.textAlign=l.textAlign(i.titleAlign),e.textBaseline="middle",o=vn(i.titleFont),r=i.titleSpacing,e.fillStyle=i.titleColor,e.font=o.string,a=0;a<s;++a)e.fillText(n[a],l.x(t.x),t.y+o.lineHeight/2),t.y+=o.lineHeight+r,a+1===s&&(t.y+=i.titleMarginBottom-r)}}_drawColorBox(t,e,i,n,s){const o=this.labelColors[i],r=this.labelPointStyles[i],{boxHeight:a,boxWidth:l}=s,h=vn(s.bodyFont),c=va(this,"left",s),d=n.x(c),u=a<h.lineHeight?(h.lineHeight-a)/2:0,f=e.y+u;if(s.usePointStyle){const e={radius:Math.min(l,a)/2,pointStyle:r.pointStyle,rotation:r.rotation,borderWidth:1},i=n.leftForLtr(d,l)+l/2,h=f+a/2;t.strokeStyle=s.multiKeyBackground,t.fillStyle=s.multiKeyBackground,tn(t,e,i,h),t.strokeStyle=o.borderColor,t.fillStyle=o.backgroundColor,tn(t,e,i,h)}else{t.lineWidth=Ae(o.borderWidth)?Math.max(...Object.values(o.borderWidth)):o.borderWidth||1,t.strokeStyle=o.borderColor,t.setLineDash(o.borderDash||[]),t.lineDashOffset=o.borderDashOffset||0;const e=n.leftForLtr(d,l),i=n.leftForLtr(n.xPlus(d,1),l-2),r=xn(o.borderRadius);Object.values(r).some(t=>0!==t)?(t.beginPath(),t.fillStyle=s.multiKeyBackground,dn(t,{x:e,y:f,w:l,h:a,radius:r}),t.fill(),t.stroke(),t.fillStyle=o.backgroundColor,t.beginPath(),dn(t,{x:i,y:f+1,w:l-2,h:a-2,radius:r}),t.fill()):(t.fillStyle=s.multiKeyBackground,t.fillRect(e,f,l,a),t.strokeRect(e,f,l,a),t.fillStyle=o.backgroundColor,t.fillRect(i,f+1,l-2,a-2))}t.fillStyle=this.labelTextColors[i]}drawBody(t,e,i){const{body:n}=this,{bodySpacing:s,bodyAlign:o,displayColors:r,boxHeight:a,boxWidth:l,boxPadding:h}=i,c=vn(i.bodyFont);let d=c.lineHeight,u=0;const f=ns(i.rtl,this.x,this.width),p=function(i){e.fillText(i,f.x(t.x+u),t.y+d/2),t.y+=d+s},g=f.textAlign(o);let m,b,x,y,v,_,w;for(e.textAlign=o,e.textBaseline="middle",e.font=c.string,t.x=va(this,g,i),e.fillStyle=i.bodyColor,Ie(this.beforeBody,p),u=r&&"right"!==g?"center"===o?l/2+h:l+2+h:0,y=0,_=n.length;y<_;++y){for(m=n[y],b=this.labelTextColors[y],e.fillStyle=b,Ie(m.before,p),x=m.lines,r&&x.length&&(this._drawColorBox(e,t,y,f,i),d=Math.max(c.lineHeight,a)),v=0,w=x.length;v<w;++v)p(x[v]),d=c.lineHeight;Ie(m.after,p)}u=0,d=c.lineHeight,Ie(this.afterBody,p),t.y-=s}drawFooter(t,e,i){const n=this.footer,s=n.length;let o,r;if(s){const a=ns(i.rtl,this.x,this.width);for(t.x=va(this,i.footerAlign,i),t.y+=i.footerMarginTop,e.textAlign=a.textAlign(i.footerAlign),e.textBaseline="middle",o=vn(i.footerFont),e.fillStyle=i.footerColor,e.font=o.string,r=0;r<s;++r)e.fillText(n[r],a.x(t.x),t.y+o.lineHeight/2),t.y+=o.lineHeight+i.footerSpacing}}drawBackground(t,e,i,n){const{xAlign:s,yAlign:o}=this,{x:r,y:a}=t,{width:l,height:h}=i,{topLeft:c,topRight:d,bottomLeft:u,bottomRight:f}=xn(n.cornerRadius);e.fillStyle=n.backgroundColor,e.strokeStyle=n.borderColor,e.lineWidth=n.borderWidth,e.beginPath(),e.moveTo(r+c,a),"top"===o&&this.drawCaret(t,e,i,n),e.lineTo(r+l-d,a),e.quadraticCurveTo(r+l,a,r+l,a+d),"center"===o&&"right"===s&&this.drawCaret(t,e,i,n),e.lineTo(r+l,a+h-f),e.quadraticCurveTo(r+l,a+h,r+l-f,a+h),"bottom"===o&&this.drawCaret(t,e,i,n),e.lineTo(r+u,a+h),e.quadraticCurveTo(r,a+h,r,a+h-u),"center"===o&&"left"===s&&this.drawCaret(t,e,i,n),e.lineTo(r,a+c),e.quadraticCurveTo(r,a,r+c,a),e.closePath(),e.fill(),n.borderWidth>0&&e.stroke()}_updateAnimationTarget(t){const e=this.chart,i=this.$animations,n=i&&i.x,s=i&&i.y;if(n||s){const i=ua[t.position].call(this,this._active,this._eventPosition);if(!i)return;const o=this._size=ma(this,t),r=Object.assign({},i,this._size),a=xa(e,t,r),l=ya(t,r,a,e);n._to===l.x&&s._to===l.y||(this.xAlign=a.xAlign,this.yAlign=a.yAlign,this.width=o.width,this.height=o.height,this.caretX=i.x,this.caretY=i.y,this._resolveAnimations().update(this,l))}}_willRender(){return!!this.opacity}draw(t){const e=this.options.setContext(this.getContext());let i=this.opacity;if(!i)return;this._updateAnimationTarget(e);const n={width:this.width,height:this.height},s={x:this.x,y:this.y};i=Math.abs(i)<.001?0:i;const o=yn(e.padding),r=this.title.length||this.beforeBody.length||this.body.length||this.afterBody.length||this.footer.length;e.enabled&&r&&(t.save(),t.globalAlpha=i,this.drawBackground(s,t,n,e),ss(t,e.textDirection),s.y+=o.top,this.drawTitle(s,t,e),this.drawBody(s,t,e),this.drawFooter(s,t,e),os(t,e.textDirection),t.restore())}getActiveElements(){return this._active||[]}setActiveElements(t,e){const i=this._active,n=t.map(({datasetIndex:t,index:e})=>{const i=this.chart.getDatasetMeta(t);if(!i)throw new Error("Cannot find a dataset at index "+t);return{datasetIndex:t,element:i.data[e],index:e}}),s=!Fe(i,n),o=this._positionChanged(n,e);(s||o)&&(this._active=n,this._eventPosition=e,this._ignoreReplayEvents=!0,this.update(!0))}handleEvent(t,e,i=!0){if(e&&this._ignoreReplayEvents)return!1;this._ignoreReplayEvents=!1;const n=this.options,s=this._active||[],o=this._getActiveElements(t,s,e,i),r=this._positionChanged(o,t),a=e||!Fe(o,s)||r;return a&&(this._active=o,(n.enabled||n.external)&&(this._eventPosition={x:t.x,y:t.y},this.update(!0,e))),a}_getActiveElements(t,e,i,n){const s=this.options;if("mouseout"===t.type)return[];if(!n)return e.filter(t=>this.chart.data.datasets[t.datasetIndex]&&void 0!==this.chart.getDatasetMeta(t.datasetIndex).controller.getParsed(t.index));const o=this.chart.getElementsAtEventForMode(t,s.mode,s,i);return s.reverse&&o.reverse(),o}_positionChanged(t,e){const{caretX:i,caretY:n,options:s}=this,o=ua[s.position].call(this,t,e);return!1!==o&&(i!==o.x||n!==o.y)}}var Oa={id:"tooltip",_element:Sa,positioners:ua,afterInit(t,e,i){i&&(t.tooltip=new Sa({chart:t,options:i}))},beforeUpdate(t,e,i){t.tooltip&&t.tooltip.initialize(i)},reset(t,e,i){t.tooltip&&t.tooltip.initialize(i)},afterDraw(t){const e=t.tooltip;if(e&&e._willRender()){const i={tooltip:e};if(!1===t.notifyPlugins("beforeTooltipDraw",{...i,cancelable:!0}))return;e.draw(t.ctx),t.notifyPlugins("afterTooltipDraw",i)}},afterEvent(t,e){if(t.tooltip){const i=e.replay;t.tooltip.handleEvent(e.event,i,e.inChartArea)&&(e.changed=!0)}},defaults:{enabled:!0,external:null,position:"average",backgroundColor:"rgba(0,0,0,0.8)",titleColor:"#fff",titleFont:{weight:"bold"},titleSpacing:2,titleMarginBottom:6,titleAlign:"left",bodyColor:"#fff",bodySpacing:2,bodyFont:{},bodyAlign:"left",footerColor:"#fff",footerSpacing:2,footerMarginTop:6,footerFont:{weight:"bold"},footerAlign:"left",padding:6,caretPadding:2,caretSize:5,cornerRadius:6,boxHeight:(t,e)=>e.bodyFont.size,boxWidth:(t,e)=>e.bodyFont.size,multiKeyBackground:"#fff",displayColors:!0,boxPadding:0,borderColor:"rgba(0,0,0,0)",borderWidth:0,animation:{duration:400,easing:"easeOutQuart"},animations:{numbers:{type:"number",properties:["x","y","width","height","caretX","caretY"]},opacity:{easing:"linear",duration:200}},callbacks:Ma},defaultRoutes:{bodyFont:"font",footerFont:"font",titleFont:"font"},descriptors:{_scriptable:t=>"filter"!==t&&"itemSort"!==t&&"external"!==t,_indexable:!1,callbacks:{_scriptable:!1,_indexable:!1},animation:{_fallback:!1},animations:{_fallback:"animation"}},additionalOptionScopes:["interaction"]},Da=Object.freeze({__proto__:null,Colors:zr,Decimation:Wr,Filler:na,Legend:aa,SubTitle:da,Title:ha,Tooltip:Oa});function Pa(t){const e=this.getLabels();return t>=0&&t<e.length?e[t]:t}function Aa(t,e,{horizontal:i,minRotation:n}){const s=hi(n),o=(i?Math.sin(s):Math.cos(s))||.001,r=.75*e*(""+t).length;return Math.min(e/o,r)}class Ca extends zo{constructor(t){super(t),this.start=void 0,this.end=void 0,this._startValue=void 0,this._endValue=void 0,this._valueRange=0}parse(t,e){return De(t)||("number"==typeof t||t instanceof Number)&&!isFinite(+t)?null:+t}handleTickRangeOptions(){const{beginAtZero:t}=this.options,{minDefined:e,maxDefined:i}=this.getUserBounds();let{min:n,max:s}=this;const o=t=>n=e?n:t,r=t=>s=i?s:t;if(t){const t=si(n),e=si(s);t<0&&e<0?r(0):t>0&&e>0&&o(0)}if(n===s){let e=0===s?1:Math.abs(.05*s);r(s+e),t||o(n-e)}this.min=n,this.max=s}getTickLimit(){const t=this.options.ticks;let e,{maxTicksLimit:i,stepSize:n}=t;return n?(e=Math.ceil(this.max/n)-Math.floor(this.min/n)+1,e>1e3&&(console.warn(`scales.${this.id}.ticks.stepSize: ${n} would result generating up to ${e} ticks. Limiting to 1000.`),e=1e3)):(e=this.computeTickLimit(),i=i||11),i&&(e=Math.min(i,e)),e}computeTickLimit(){return Number.POSITIVE_INFINITY}buildTicks(){const t=this.options,e=t.ticks;let i=this.getTickLimit();i=Math.max(2,i);const n=function(t,e){const i=[],{bounds:n,step:s,min:o,max:r,precision:a,count:l,maxTicks:h,maxDigits:c,includeBounds:d}=t,u=s||1,f=h-1,{min:p,max:g}=e,m=!De(o),b=!De(r),x=!De(l),y=(g-p)/(c+1);let v,_,w,M,k=ri((g-p)/f/u)*u;if(k<1e-14&&!m&&!b)return[{value:p},{value:g}];M=Math.ceil(g/k)-Math.floor(p/k),M>f&&(k=ri(M*k/f/u)*u),De(a)||(v=Math.pow(10,a),k=Math.ceil(k*v)/v),"ticks"===n?(_=Math.floor(p/k)*k,w=Math.ceil(g/k)*k):(_=p,w=g),m&&b&&s&&function(t,e){const i=Math.round(t);return i-e<=t&&i+e>=t}((r-o)/s,k/1e3)?(M=Math.round(Math.min((r-o)/k,h)),k=(r-o)/M,_=o,w=r):x?(_=m?o:_,w=b?r:w,M=l-1,k=(w-_)/M):(M=(w-_)/k,M=oi(M,Math.round(M),k/1e3)?Math.round(M):Math.ceil(M));const S=Math.max(di(k),di(_));v=Math.pow(10,De(a)?S:a),_=Math.round(_*v)/v,w=Math.round(w*v)/v;let O=0;for(m&&(d&&_!==o?(i.push({value:o}),_<o&&O++,oi(Math.round((_+O*k)*v)/v,o,Aa(o,y,t))&&O++):_<o&&O++);O<M;++O){const t=Math.round((_+O*k)*v)/v;if(b&&t>r)break;i.push({value:t})}return b&&d&&w!==r?i.length&&oi(i[i.length-1].value,r,Aa(r,y,t))?i[i.length-1].value=r:i.push({value:r}):b&&w!==r||i.push({value:w}),i}({maxTicks:i,bounds:t.bounds,min:t.min,max:t.max,precision:e.precision,step:e.stepSize,count:e.count,maxDigits:this._maxDigits(),horizontal:this.isHorizontal(),minRotation:e.minRotation||0,includeBounds:!1!==e.includeBounds},this._range||this);return"ticks"===t.bounds&&li(n,this,"value"),t.reverse?(n.reverse(),this.start=this.max,this.end=this.min):(this.start=this.min,this.end=this.max),n}configure(){const t=this.ticks;let e=this.min,i=this.max;if(super.configure(),this.options.offset&&t.length){const n=(i-e)/Math.max(t.length-1,1)/2;e-=n,i+=n}this._startValue=e,this._endValue=i,this._valueRange=i-e}getLabelForValue(t){return ji(t,this.chart.options.locale,this.options.ticks.format)}}class Ta extends Ca{static id="linear";static defaults={ticks:{callback:Hi.formatters.numeric}};determineDataLimits(){const{min:t,max:e}=this.getMinMax(!0);this.min=Ce(t)?t:0,this.max=Ce(e)?e:1,this.handleTickRangeOptions()}computeTickLimit(){const t=this.isHorizontal(),e=t?this.width:this.height,i=hi(this.options.ticks.minRotation),n=(t?Math.sin(i):Math.cos(i))||.001,s=this._resolveTickFontOptions(0);return Math.ceil(e/Math.min(40,s.lineHeight/n))}getPixelForValue(t){return null===t?NaN:this.getPixelForDecimal((t-this._startValue)/this._valueRange)}getValueForPixel(t){return this._startValue+this.getDecimalForPixel(t)*this._valueRange}}const Ea=t=>Math.floor(ni(t)),La=(t,e)=>Math.pow(10,Ea(t)+e);function Ra(t){return 1===t/Math.pow(10,Ea(t))}function Ia(t,e,i){const n=Math.pow(10,i),s=Math.floor(t/n);return Math.ceil(e/n)-s}class Fa extends zo{static id="logarithmic";static defaults={ticks:{callback:Hi.formatters.logarithmic,major:{enabled:!0}}};constructor(t){super(t),this.start=void 0,this.end=void 0,this._startValue=void 0,this._valueRange=0}parse(t,e){const i=Ca.prototype.parse.apply(this,[t,e]);if(0!==i)return Ce(i)&&i>0?i:null;this._zero=!0}determineDataLimits(){const{min:t,max:e}=this.getMinMax(!0);this.min=Ce(t)?Math.max(0,t):null,this.max=Ce(e)?Math.max(0,e):null,this.options.beginAtZero&&(this._zero=!0),this._zero&&this.min!==this._suggestedMin&&!Ce(this._userMin)&&(this.min=t===La(this.min,0)?La(this.min,-1):La(this.min,0)),this.handleTickRangeOptions()}handleTickRangeOptions(){const{minDefined:t,maxDefined:e}=this.getUserBounds();let i=this.min,n=this.max;const s=e=>i=t?i:e,o=t=>n=e?n:t;i===n&&(i<=0?(s(1),o(10)):(s(La(i,-1)),o(La(n,1)))),i<=0&&s(La(n,-1)),n<=0&&o(La(i,1)),this.min=i,this.max=n}buildTicks(){const t=this.options,e=function(t,{min:e,max:i}){e=Te(t.min,e);const n=[],s=Ea(e);let o=function(t,e){let i=Ea(e-t);for(;Ia(t,e,i)>10;)i++;for(;Ia(t,e,i)<10;)i--;return Math.min(i,Ea(t))}(e,i),r=o<0?Math.pow(10,Math.abs(o)):1;const a=Math.pow(10,o),l=s>o?Math.pow(10,s):0,h=Math.round((e-l)*r)/r,c=Math.floor((e-l)/a/10)*a*10;let d=Math.floor((h-c)/Math.pow(10,o)),u=Te(t.min,Math.round((l+c+d*Math.pow(10,o))*r)/r);for(;u<i;)n.push({value:u,major:Ra(u),significand:d}),d>=10?d=d<15?15:20:d++,d>=20&&(o++,d=2,r=o>=0?1:r),u=Math.round((l+c+d*Math.pow(10,o))*r)/r;const f=Te(t.max,u);return n.push({value:f,major:Ra(f),significand:d}),n}({min:this._userMin,max:this._userMax},this);return"ticks"===t.bounds&&li(e,this,"value"),t.reverse?(e.reverse(),this.start=this.max,this.end=this.min):(this.start=this.min,this.end=this.max),e}getLabelForValue(t){return void 0===t?"0":ji(t,this.chart.options.locale,this.options.ticks.format)}configure(){const t=this.min;super.configure(),this._startValue=ni(t),this._valueRange=ni(this.max)-ni(t)}getPixelForValue(t){return void 0!==t&&0!==t||(t=this.min),null===t||isNaN(t)?NaN:this.getPixelForDecimal(t===this.min?0:(ni(t)-this._startValue)/this._valueRange)}getValueForPixel(t){const e=this.getDecimalForPixel(t);return Math.pow(10,this._startValue+e*this._valueRange)}}function za(t){const e=t.ticks;if(e.display&&t.display){const t=yn(e.backdropPadding);return Ee(e.font&&e.font.size,Ki.font.size)+t.height}return 0}function Va(t,e,i){return i=Pe(i)?i:[i],{w:Ji(t,e.string,i),h:i.length*e.lineHeight}}function Ba(t,e,i,n,s){return t===n||t===s?{start:e-i/2,end:e+i/2}:t<n||t>s?{start:e-i,end:e}:{start:e,end:e+i}}function Wa(t,e,i,n,s){const o=Math.abs(Math.sin(i)),r=Math.abs(Math.cos(i));let a=0,l=0;n.start<e.l?(a=(e.l-n.start)/o,t.l=Math.min(t.l,e.l-a)):n.end>e.r&&(a=(n.end-e.r)/o,t.r=Math.max(t.r,e.r+a)),s.start<e.t?(l=(e.t-s.start)/r,t.t=Math.min(t.t,e.t-l)):s.end>e.b&&(l=(s.end-e.b)/r,t.b=Math.max(t.b,e.b+l))}function ja(t,e,i){const n=t.drawingArea,{extra:s,additionalAngle:o,padding:r,size:a}=i,l=t.getPointPosition(e,n+s+r,o),h=Math.round(ci(gi(l.angle+ti))),c=function(t,e,i){return 90===i||270===i?t-=e/2:(i>270||i<90)&&(t-=e),t}(l.y,a.h,h),d=function(t){return 0===t||180===t?"center":t<180?"left":"right"}(h),u=(f=l.x,p=a.w,"right"===(g=d)?f-=p:"center"===g&&(f-=p/2),f);var f,p,g;return{visible:!0,x:l.x,y:c,textAlign:d,left:u,top:c,right:u+a.w,bottom:c+a.h}}function Na(t,e){if(!e)return!0;const{left:i,top:n,right:s,bottom:o}=t;return!(nn({x:i,y:n},e)||nn({x:i,y:o},e)||nn({x:s,y:n},e)||nn({x:s,y:o},e))}function Ha(t,e,i){const{left:n,top:s,right:o,bottom:r}=i,{backdropColor:a}=e;if(!De(a)){const i=xn(e.borderRadius),l=yn(e.backdropPadding);t.fillStyle=a;const h=n-l.left,c=s-l.top,d=o-n+l.width,u=r-s+l.height;Object.values(i).some(t=>0!==t)?(t.beginPath(),dn(t,{x:h,y:c,w:d,h:u,radius:i}),t.fill()):t.fillRect(h,c,d,u)}}function $a(t,e,i,n){const{ctx:s}=t;if(i)s.arc(t.xCenter,t.yCenter,e,0,Ge);else{let i=t.getPointPosition(0,e);s.moveTo(i.x,i.y);for(let o=1;o<n;o++)i=t.getPointPosition(o,e),s.lineTo(i.x,i.y)}}class Ua extends Ca{static id="radialLinear";static defaults={display:!0,animate:!0,position:"chartArea",angleLines:{display:!0,lineWidth:1,borderDash:[],borderDashOffset:0},grid:{circular:!1},startAngle:0,ticks:{showLabelBackdrop:!0,callback:Hi.formatters.numeric},pointLabels:{backdropColor:void 0,backdropPadding:2,display:!0,font:{size:10},callback(t){return t},padding:5,centerPointLabels:!1}};static defaultRoutes={"angleLines.color":"borderColor","pointLabels.color":"color","ticks.color":"color"};static descriptors={angleLines:{_fallback:"grid"}};constructor(t){super(t),this.xCenter=void 0,this.yCenter=void 0,this.drawingArea=void 0,this._pointLabels=[],this._pointLabelItems=[]}setDimensions(){const t=this._padding=yn(za(this.options)/2),e=this.width=this.maxWidth-t.width,i=this.height=this.maxHeight-t.height;this.xCenter=Math.floor(this.left+e/2+t.left),this.yCenter=Math.floor(this.top+i/2+t.top),this.drawingArea=Math.floor(Math.min(e,i)/2)}determineDataLimits(){const{min:t,max:e}=this.getMinMax(!1);this.min=Ce(t)&&!isNaN(t)?t:0,this.max=Ce(e)&&!isNaN(e)?e:0,this.handleTickRangeOptions()}computeTickLimit(){return Math.ceil(this.drawingArea/za(this.options))}generateTickLabels(t){Ca.prototype.generateTickLabels.call(this,t),this._pointLabels=this.getLabels().map((t,e)=>{const i=Re(this.options.pointLabels.callback,[t,e],this);return i||0===i?i:""}).filter((t,e)=>this.chart.getDataVisibility(e))}fit(){const t=this.options;t.display&&t.pointLabels.display?function(t){const e={l:t.left+t._padding.left,r:t.right-t._padding.right,t:t.top+t._padding.top,b:t.bottom-t._padding.bottom},i=Object.assign({},e),n=[],s=[],o=t._pointLabels.length,r=t.options.pointLabels,a=r.centerPointLabels?Ke/o:0;for(let l=0;l<o;l++){const o=r.setContext(t.getPointLabelContext(l));s[l]=o.padding;const h=t.getPointPosition(l,t.drawingArea+s[l],a),c=vn(o.font),d=Va(t.ctx,c,t._pointLabels[l]);n[l]=d;const u=gi(t.getIndexAngle(l)+a),f=Math.round(ci(u));Wa(i,e,u,Ba(f,h.x,d.w,0,180),Ba(f,h.y,d.h,90,270))}t.setCenterPoint(e.l-i.l,i.r-e.r,e.t-i.t,i.b-e.b),t._pointLabelItems=function(t,e,i){const n=[],s=t._pointLabels.length,o=t.options,{centerPointLabels:r,display:a}=o.pointLabels,l={extra:za(o)/2,additionalAngle:r?Ke/s:0};let h;for(let o=0;o<s;o++){l.padding=i[o],l.size=e[o];const s=ja(t,o,l);n.push(s),"auto"===a&&(s.visible=Na(s,h),s.visible&&(h=s))}return n}(t,n,s)}(this):this.setCenterPoint(0,0,0,0)}setCenterPoint(t,e,i,n){this.xCenter+=Math.floor((t-e)/2),this.yCenter+=Math.floor((i-n)/2),this.drawingArea-=Math.min(this.drawingArea/2,Math.max(t,e,i,n))}getIndexAngle(t){return gi(t*(Ge/(this._pointLabels.length||1))+hi(this.options.startAngle||0))}getDistanceFromCenterForValue(t){if(De(t))return NaN;const e=this.drawingArea/(this.max-this.min);return this.options.reverse?(this.max-t)*e:(t-this.min)*e}getValueForDistanceFromCenter(t){if(De(t))return NaN;const e=t/(this.drawingArea/(this.max-this.min));return this.options.reverse?this.max-e:this.min+e}getPointLabelContext(t){const e=this._pointLabels||[];if(t>=0&&t<e.length){const i=e[t];return function(t,e,i){return wn(t,{label:i,index:e,type:"pointLabel"})}(this.getContext(),t,i)}}getPointPosition(t,e,i=0){const n=this.getIndexAngle(t)-ti+i;return{x:Math.cos(n)*e+this.xCenter,y:Math.sin(n)*e+this.yCenter,angle:n}}getPointPositionForValue(t,e){return this.getPointPosition(t,this.getDistanceFromCenterForValue(e))}getBasePosition(t){return this.getPointPositionForValue(t||0,this.getBaseValue())}getPointLabelPosition(t){const{left:e,top:i,right:n,bottom:s}=this._pointLabelItems[t];return{left:e,top:i,right:n,bottom:s}}drawBackground(){const{backgroundColor:t,grid:{circular:e}}=this.options;if(t){const i=this.ctx;i.save(),i.beginPath(),$a(this,this.getDistanceFromCenterForValue(this._endValue),e,this._pointLabels.length),i.closePath(),i.fillStyle=t,i.fill(),i.restore()}}drawGrid(){const t=this.ctx,e=this.options,{angleLines:i,grid:n,border:s}=e,o=this._pointLabels.length;let r,a,l;if(e.pointLabels.display&&function(t,e){const{ctx:i,options:{pointLabels:n}}=t;for(let s=e-1;s>=0;s--){const e=t._pointLabelItems[s];if(!e.visible)continue;const o=n.setContext(t.getPointLabelContext(s));Ha(i,o,e);const r=vn(o.font),{x:a,y:l,textAlign:h}=e;cn(i,t._pointLabels[s],a,l+r.lineHeight/2,r,{color:o.color,textAlign:h,textBaseline:"middle"})}}(this,o),n.display&&this.ticks.forEach((t,e)=>{if(0!==e||0===e&&this.min<0){a=this.getDistanceFromCenterForValue(t.value);const i=this.getContext(e),r=n.setContext(i),l=s.setContext(i);!function(t,e,i,n,s){const o=t.ctx,r=e.circular,{color:a,lineWidth:l}=e;!r&&!n||!a||!l||i<0||(o.save(),o.strokeStyle=a,o.lineWidth=l,o.setLineDash(s.dash||[]),o.lineDashOffset=s.dashOffset,o.beginPath(),$a(t,i,r,n),o.closePath(),o.stroke(),o.restore())}(this,r,a,o,l)}}),i.display){for(t.save(),r=o-1;r>=0;r--){const n=i.setContext(this.getPointLabelContext(r)),{color:s,lineWidth:o}=n;o&&s&&(t.lineWidth=o,t.strokeStyle=s,t.setLineDash(n.borderDash),t.lineDashOffset=n.borderDashOffset,a=this.getDistanceFromCenterForValue(e.reverse?this.min:this.max),l=this.getPointPosition(r,a),t.beginPath(),t.moveTo(this.xCenter,this.yCenter),t.lineTo(l.x,l.y),t.stroke())}t.restore()}}drawBorder(){}drawLabels(){const t=this.ctx,e=this.options,i=e.ticks;if(!i.display)return;const n=this.getIndexAngle(0);let s,o;t.save(),t.translate(this.xCenter,this.yCenter),t.rotate(n),t.textAlign="center",t.textBaseline="middle",this.ticks.forEach((n,r)=>{if(0===r&&this.min>=0&&!e.reverse)return;const a=i.setContext(this.getContext(r)),l=vn(a.font);if(s=this.getDistanceFromCenterForValue(this.ticks[r].value),a.showLabelBackdrop){t.font=l.string,o=t.measureText(n.label).width,t.fillStyle=a.backdropColor;const e=yn(a.backdropPadding);t.fillRect(-o/2-e.left,-s-l.size/2-e.top,o+e.width,l.size+e.height)}cn(t,n.label,0,-s,l,{color:a.color,strokeColor:a.textStrokeColor,strokeWidth:a.textStrokeWidth})}),t.restore()}drawTitle(){}}const Ya={millisecond:{common:!0,size:1,steps:1e3},second:{common:!0,size:1e3,steps:60},minute:{common:!0,size:6e4,steps:60},hour:{common:!0,size:36e5,steps:24},day:{common:!0,size:864e5,steps:30},week:{common:!1,size:6048e5,steps:4},month:{common:!0,size:2628e6,steps:12},quarter:{common:!1,size:7884e6,steps:4},year:{common:!0,size:3154e7}},Xa=Object.keys(Ya);function qa(t,e){return t-e}function Ka(t,e){if(De(e))return null;const i=t._adapter,{parser:n,round:s,isoWeekday:o}=t._parseOpts;let r=e;return"function"==typeof n&&(r=n(r)),Ce(r)||(r="string"==typeof n?i.parse(r,n):i.parse(r)),null===r?null:(s&&(r="week"!==s||!ai(o)&&!0!==o?i.startOf(r,s):i.startOf(r,"isoWeek",o)),+r)}function Ga(t,e,i,n){const s=Xa.length;for(let o=Xa.indexOf(t);o<s-1;++o){const t=Ya[Xa[o]],s=t.steps?t.steps:Number.MAX_SAFE_INTEGER;if(t.common&&Math.ceil((i-e)/(s*t.size))<=n)return Xa[o]}return Xa[s-1]}function Ja(t,e,i){if(i){if(i.length){const{lo:n,hi:s}=yi(i,e);t[i[n]>=e?i[n]:i[s]]=!0}}else t[e]=!0}function Za(t,e,i){const n=[],s={},o=e.length;let r,a;for(r=0;r<o;++r)a=e[r],s[a]=r,n.push({value:a,major:!1});return 0!==o&&i?function(t,e,i,n){const s=t._adapter,o=+s.startOf(e[0].value,n),r=e[e.length-1].value;let a,l;for(a=o;a<=r;a=+s.add(a,1,n))l=i[a],l>=0&&(e[l].major=!0);return e}(t,n,s,i):n}class Qa extends zo{static id="time";static defaults={bounds:"data",adapters:{},time:{parser:!1,unit:!1,round:!1,isoWeekday:!1,minUnit:"millisecond",displayFormats:{}},ticks:{source:"auto",callback:!1,major:{enabled:!1}}};constructor(t){super(t),this._cache={data:[],labels:[],all:[]},this._unit="day",this._majorUnit=void 0,this._offsets={},this._normalized=!1,this._parseOpts=void 0}init(t,e={}){const i=t.time||(t.time={}),n=this._adapter=new Us(t.adapters.date);n.init(e),je(i.displayFormats,n.formats()),this._parseOpts={parser:i.parser,round:i.round,isoWeekday:i.isoWeekday},super.init(t),this._normalized=e.normalized}parse(t,e){return void 0===t?null:Ka(this,t)}beforeLayout(){super.beforeLayout(),this._cache={data:[],labels:[],all:[]}}determineDataLimits(){const t=this.options,e=this._adapter,i=t.time.unit||"day";let{min:n,max:s,minDefined:o,maxDefined:r}=this.getUserBounds();function a(t){o||isNaN(t.min)||(n=Math.min(n,t.min)),r||isNaN(t.max)||(s=Math.max(s,t.max))}o&&r||(a(this._getLabelBounds()),"ticks"===t.bounds&&"labels"===t.ticks.source||a(this.getMinMax(!1))),n=Ce(n)&&!isNaN(n)?n:+e.startOf(Date.now(),i),s=Ce(s)&&!isNaN(s)?s:+e.endOf(Date.now(),i)+1,this.min=Math.min(n,s-1),this.max=Math.max(n+1,s)}_getLabelBounds(){const t=this.getLabelTimestamps();let e=Number.POSITIVE_INFINITY,i=Number.NEGATIVE_INFINITY;return t.length&&(e=t[0],i=t[t.length-1]),{min:e,max:i}}buildTicks(){const t=this.options,e=t.time,i=t.ticks,n="labels"===i.source?this.getLabelTimestamps():this._generate();"ticks"===t.bounds&&n.length&&(this.min=this._userMin||n[0],this.max=this._userMax||n[n.length-1]);const s=this.min,o=function(t,e,i){let n=0,s=t.length;for(;n<s&&t[n]<e;)n++;for(;s>n&&t[s-1]>i;)s--;return n>0||s<t.length?t.slice(n,s):t}(n,s,this.max);return this._unit=e.unit||(i.autoSkip?Ga(e.minUnit,this.min,this.max,this._getLabelCapacity(s)):function(t,e,i,n,s){for(let o=Xa.length-1;o>=Xa.indexOf(i);o--){const i=Xa[o];if(Ya[i].common&&t._adapter.diff(s,n,i)>=e-1)return i}return Xa[i?Xa.indexOf(i):0]}(this,o.length,e.minUnit,this.min,this.max)),this._majorUnit=i.major.enabled&&"year"!==this._unit?function(t){for(let e=Xa.indexOf(t)+1,i=Xa.length;e<i;++e)if(Ya[Xa[e]].common)return Xa[e]}(this._unit):void 0,this.initOffsets(n),t.reverse&&o.reverse(),Za(this,o,this._majorUnit)}afterAutoSkip(){this.options.offsetAfterAutoskip&&this.initOffsets(this.ticks.map(t=>+t.value))}initOffsets(t=[]){let e,i,n=0,s=0;this.options.offset&&t.length&&(e=this.getDecimalForValue(t[0]),n=1===t.length?1-e:(this.getDecimalForValue(t[1])-e)/2,i=this.getDecimalForValue(t[t.length-1]),s=1===t.length?i:(i-this.getDecimalForValue(t[t.length-2]))/2);const o=t.length<3?.5:.25;n=bi(n,0,o),s=bi(s,0,o),this._offsets={start:n,end:s,factor:1/(n+1+s)}}_generate(){const t=this._adapter,e=this.min,i=this.max,n=this.options,s=n.time,o=s.unit||Ga(s.minUnit,e,i,this._getLabelCapacity(e)),r=Ee(n.ticks.stepSize,1),a="week"===o&&s.isoWeekday,l=ai(a)||!0===a,h={};let c,d,u=e;if(l&&(u=+t.startOf(u,"isoWeek",a)),u=+t.startOf(u,l?"day":o),t.diff(i,e,o)>1e5*r)throw new Error(e+" and "+i+" are too far apart with stepSize of "+r+" "+o);const f="data"===n.ticks.source&&this.getDataTimestamps();for(c=u,d=0;c<i;c=+t.add(c,r,o),d++)Ja(h,c,f);return c!==i&&"ticks"!==n.bounds&&1!==d||Ja(h,c,f),Object.keys(h).sort(qa).map(t=>+t)}getLabelForValue(t){const e=this._adapter,i=this.options.time;return i.tooltipFormat?e.format(t,i.tooltipFormat):e.format(t,i.displayFormats.datetime)}format(t,e){const i=this.options.time.displayFormats,n=this._unit,s=e||i[n];return this._adapter.format(t,s)}_tickFormatFunction(t,e,i,n){const s=this.options,o=s.ticks.callback;if(o)return Re(o,[t,e,i],this);const r=s.time.displayFormats,a=this._unit,l=this._majorUnit,h=a&&r[a],c=l&&r[l],d=i[e],u=l&&c&&d&&d.major;return this._adapter.format(t,n||(u?c:h))}generateTickLabels(t){let e,i,n;for(e=0,i=t.length;e<i;++e)n=t[e],n.label=this._tickFormatFunction(n.value,e,t)}getDecimalForValue(t){return null===t?NaN:(t-this.min)/(this.max-this.min)}getPixelForValue(t){const e=this._offsets,i=this.getDecimalForValue(t);return this.getPixelForDecimal((e.start+i)*e.factor)}getValueForPixel(t){const e=this._offsets,i=this.getDecimalForPixel(t)/e.factor-e.end;return this.min+i*(this.max-this.min)}_getLabelSize(t){const e=this.options.ticks,i=this.ctx.measureText(t).width,n=hi(this.isHorizontal()?e.maxRotation:e.minRotation),s=Math.cos(n),o=Math.sin(n),r=this._resolveTickFontOptions(0).size;return{w:i*s+r*o,h:i*o+r*s}}_getLabelCapacity(t){const e=this.options.time,i=e.displayFormats,n=i[e.unit]||i.millisecond,s=this._tickFormatFunction(t,0,Za(this,[t],this._majorUnit),n),o=this._getLabelSize(s),r=Math.floor(this.isHorizontal()?this.width/o.w:this.height/o.h)-1;return r>0?r:1}getDataTimestamps(){let t,e,i=this._cache.data||[];if(i.length)return i;const n=this.getMatchingVisibleMetas();if(this._normalized&&n.length)return this._cache.data=n[0].controller.getAllParsedValues(this);for(t=0,e=n.length;t<e;++t)i=i.concat(n[t].controller.getAllParsedValues(this));return this._cache.data=this.normalize(i)}getLabelTimestamps(){const t=this._cache.labels||[];let e,i;if(t.length)return t;const n=this.getLabels();for(e=0,i=n.length;e<i;++e)t.push(Ka(this,n[e]));return this._cache.labels=this._normalized?t:this.normalize(t)}normalize(t){return ki(t.sort(qa))}}function tl(t,e,i){let n,s,o,r,a=0,l=t.length-1;i?(e>=t[a].pos&&e<=t[l].pos&&({lo:a,hi:l}=vi(t,"pos",e)),({pos:n,time:o}=t[a]),({pos:s,time:r}=t[l])):(e>=t[a].time&&e<=t[l].time&&({lo:a,hi:l}=vi(t,"time",e)),({time:n,pos:o}=t[a]),({time:s,pos:r}=t[l]));const h=s-n;return h?o+(r-o)*(e-n)/h:o}var el=Object.freeze({__proto__:null,CategoryScale:class extends zo{static id="category";static defaults={ticks:{callback:Pa}};constructor(t){super(t),this._startValue=void 0,this._valueRange=0,this._addedLabels=[]}init(t){const e=this._addedLabels;if(e.length){const t=this.getLabels();for(const{index:i,label:n}of e)t[i]===n&&t.splice(i,1);this._addedLabels=[]}super.init(t)}parse(t,e){if(De(t))return null;const i=this.getLabels();return((t,e)=>null===t?null:bi(Math.round(t),0,e))(e=isFinite(e)&&i[e]===t?e:function(t,e,i,n){const s=t.indexOf(e);return-1===s?((t,e,i,n)=>("string"==typeof e?(i=t.push(e)-1,n.unshift({index:i,label:e})):isNaN(e)&&(i=null),i))(t,e,i,n):s!==t.lastIndexOf(e)?i:s}(i,t,Ee(e,t),this._addedLabels),i.length-1)}determineDataLimits(){const{minDefined:t,maxDefined:e}=this.getUserBounds();let{min:i,max:n}=this.getMinMax(!0);"ticks"===this.options.bounds&&(t||(i=0),e||(n=this.getLabels().length-1)),this.min=i,this.max=n}buildTicks(){const t=this.min,e=this.max,i=this.options.offset,n=[];let s=this.getLabels();s=0===t&&e===s.length-1?s:s.slice(t,e+1),this._valueRange=Math.max(s.length-(i?0:1),1),this._startValue=this.min-(i?.5:0);for(let i=t;i<=e;i++)n.push({value:i});return n}getLabelForValue(t){return Pa.call(this,t)}configure(){super.configure(),this.isHorizontal()||(this._reversePixels=!this._reversePixels)}getPixelForValue(t){return"number"!=typeof t&&(t=this.parse(t)),null===t?NaN:this.getPixelForDecimal((t-this._startValue)/this._valueRange)}getPixelForTick(t){const e=this.ticks;return t<0||t>e.length-1?null:this.getPixelForValue(e[t].value)}getValueForPixel(t){return Math.round(this._startValue+this.getDecimalForPixel(t)*this._valueRange)}getBasePixel(){return this.bottom}},LinearScale:Ta,LogarithmicScale:Fa,RadialLinearScale:Ua,TimeScale:Qa,TimeSeriesScale:class extends Qa{static id="timeseries";static defaults=Qa.defaults;constructor(t){super(t),this._table=[],this._minPos=void 0,this._tableRange=void 0}initOffsets(){const t=this._getTimestampsForTable(),e=this._table=this.buildLookupTable(t);this._minPos=tl(e,this.min),this._tableRange=tl(e,this.max)-this._minPos,super.initOffsets(t)}buildLookupTable(t){const{min:e,max:i}=this,n=[],s=[];let o,r,a,l,h;for(o=0,r=t.length;o<r;++o)l=t[o],l>=e&&l<=i&&n.push(l);if(n.length<2)return[{time:e,pos:0},{time:i,pos:1}];for(o=0,r=n.length;o<r;++o)h=n[o+1],a=n[o-1],l=n[o],Math.round((h+a)/2)!==l&&s.push({time:l,pos:o/(r-1)});return s}_generate(){const t=this.min,e=this.max;let i=super.getDataTimestamps();return i.includes(t)&&i.length||i.splice(0,0,t),i.includes(e)&&1!==i.length||i.push(e),i.sort((t,e)=>t-e)}_getTimestampsForTable(){let t=this._cache.all||[];if(t.length)return t;const e=this.getDataTimestamps(),i=this.getLabelTimestamps();return t=e.length&&i.length?this.normalize(e.concat(i)):e.length?e:i,t=this._cache.all=t,t}getDecimalForValue(t){return(tl(this._table,t)-this._minPos)/this._tableRange}getValueForPixel(t){const e=this._offsets,i=this.getDecimalForPixel(t)/e.factor-e.end;return tl(this._table,i*this._tableRange+this._minPos,!0)}}});const il=[Ns,Tr,Da,el];fr.register(...il);var nl=fr;!function(){const t=[],e=[],i=new Date;for(let n=29;n>=0;n--){const s=new Date(i);s.setHours(12,0,0,0),s.setDate(i.getDate()-n),t.push(s.toLocaleDateString(void 0,{month:"short",day:"numeric"})),e.push(s)}const n=document.getElementById("ar-response-time-chart");if(n&&nl){const e=n.getContext("2d"),i=[320,310,305,295,290,300,315,310,305,520,340,330,325,315,310,300,295,290,285,280,275,270,265,600,320,315,310,305,300,295],s=(()=>{const t=e.createLinearGradient(0,0,0,n.height||220);return t.addColorStop(1,"rgba(34,113,177,0.00)"),t.addColorStop(0,"rgba(34,113,177,0.25)"),t})(),o={id:"hoverLine",afterDatasetsDraw(t){const e=t.tooltip?.getActiveElements?.();if(!e||0===e.length)return;const{ctx:i,chartArea:{top:n,bottom:s},scales:{x:o}}=t,r=e[0].index,a=o.getPixelForValue(r);i.save(),i.beginPath(),i.moveTo(a,n),i.lineTo(a,s),i.lineWidth=2,i.strokeStyle="rgba(0,0,0,0.1)",i.setLineDash([8,6]),i.stroke(),i.setLineDash([]),i.restore()}};new nl(e,{type:"line",data:{labels:t,datasets:[{label:"Response time (ms)",data:i,borderColor:"rgba(34, 113, 177, 1)",backgroundColor:s,fill:!0,tension:.25,pointRadius:0,pointHoverRadius:6,pointHoverBorderWidth:3,pointHoverBackgroundColor:"rgba(34,113,177,1)",pointHoverBorderColor:"#e2ecf5",borderWidth:2,hoverBorderWidth:3,clip:!1}]},options:{responsive:!0,maintainAspectRatio:!1,interaction:{mode:"index",intersect:!1},layout:{padding:{top:0,right:0,bottom:15,left:0}},scales:{y:{beginAtZero:!0,ticks:{callback:t=>t+"ms",padding:15},grid:{color:"rgba(0,0,0,0.05)",drawTicks:!1},border:{display:!1}},x:{grid:{display:!1},border:{display:!1}}},plugins:{legend:{display:!1},tooltip:{enabled:!0,backgroundColor:"#ffffff",titleColor:"#111827",bodyColor:"#111827",borderColor:"#e5e7eb",borderWidth:1,padding:10,displayColors:!1,caretPadding:15,callbacks:{title:t=>t?.[0]?.label??"",label:t=>`${t.parsed.y}ms`},titleFont:{size:12,weight:"normal"},bodyFont:{size:14,weight:"bold"}}}},plugins:[o]})}const s=document.getElementById("ar-uptime-pills");if(s){const e=new Set([9,23]),i=[];for(let n=0;n<30;n++){const o=e.has(n),r=document.createElement("div");r.className="ar-pill "+(o?"down":"up");const a=t[n],l=o?`${a}<br><strong style="font-size: 14px;">Down 5m 3s</strong>`:`${a}<br><strong style="font-size: 14px;">Up 100%</strong>`;i.push({element:r,tooltip:l,label:a}),r.setAttribute("aria-label",l),s.appendChild(r)}const n=qt(s,{content:"",allowHTML:!0,theme:"ar-light",hideOnClick:!1,trigger:"manual",animation:!1,placement:"top",offset:[0,8],arrow:!0,appendTo:()=>document.body,duration:[0,0],getReferenceClientRect:()=>o>=0&&o<i.length?i[o].element.getBoundingClientRect():s.getBoundingClientRect()});let o=-1;s.addEventListener("mousemove",t=>{const e=s.getBoundingClientRect(),r=t.clientX-e.left,a=e.width/30,l=Math.floor(r/a);l>=0&&l<30&&l!==o&&(o=l,n.setContent(i[l].tooltip),n.state.isVisible?n.popperInstance.update():n.show(),i.forEach((t,e)=>{t.element.style.opacity=e===l?"0.7":""}))}),s.addEventListener("mouseleave",()=>{o=-1,n.hide(),i.forEach(t=>{t.element.style.opacity=""})})}}()}();