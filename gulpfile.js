const gulp = require('gulp');
const sass = require('gulp-sass')(require('sass'));
const sourcemaps = require('gulp-sourcemaps');
const postcss = require('gulp-postcss');
const autoprefixer = require('autoprefixer');
const cleanCSS = require('gulp-clean-css');
const browserSync = require('browser-sync').create();
const webpack = require('webpack-stream');

const paths = {
  scss: 'src/scss/admin.scss',
  scssWatch: 'src/scss/**/*.scss',
  cssDest: 'assets/css',
  jsSrc: 'src/js/admin.js',
  jsWatch: 'src/js/**/*.js',
  jsDest: 'assets/js',
};

function stylesDev() {
  return gulp.src(paths.scss)
    .pipe(sourcemaps.init())
    .pipe(sass().on('error', sass.logError))
    .pipe(postcss([autoprefixer()]))
    .pipe(sourcemaps.write('.'))
    .pipe(gulp.dest(paths.cssDest))
    .pipe(browserSync.stream({ match: '**/*.css' }));
}

function stylesBuild() {
  return gulp.src(paths.scss)
    .pipe(sass().on('error', sass.logError))
    .pipe(postcss([autoprefixer()]))
    .pipe(cleanCSS({ level: 2 }))
    .pipe(gulp.dest(paths.cssDest));
}

function scriptsDev() {
  return gulp.src(paths.jsSrc)
    .pipe(webpack({
      mode: 'development',
      output: {
        filename: 'admin.js'
      },
      devtool: 'source-map'
    }))
    .pipe(gulp.dest(paths.jsDest))
    .pipe(browserSync.stream());
}

function scriptsBuild() {
  return gulp.src(paths.jsSrc)
    .pipe(webpack({
      mode: 'production',
      output: {
        filename: 'admin.js'
      }
    }))
    .pipe(gulp.dest(paths.jsDest));
}

function serve() {
  browserSync.init({
    proxy: 'https://wordpress.test/',
    host: 'wordpress.test',
    open: false,
    port: 8080,
    https: {
        key: "/Users/<USER>/Library/Application Support/Herd/config/valet/Certificates/wordpress.test.key",
        cert: "/Users/<USER>/Library/Application Support/Herd/config/valet/Certificates/wordpress.test.crt"
    },
    files: [
      'awesome-reports.php',
      'assets/js/**/*.js',
      'assets/css/**/*.css',
    ],
    snippetOptions: {
      rule: {
        match: /<\/body>/i,
        fn: function (snippet, match) {
          return snippet + match;
        }
      }
    }
  });

  gulp.watch(paths.scssWatch, stylesDev);
  gulp.watch(paths.jsWatch, scriptsDev);
}

const dev = gulp.series(gulp.parallel(stylesDev, scriptsDev), serve);
const build = gulp.series(gulp.parallel(stylesBuild, scriptsBuild));

exports.dev = dev;
exports.build = build;
exports.default = dev;

